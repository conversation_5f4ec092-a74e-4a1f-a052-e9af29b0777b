package bolt

import (
	"github.com/semaphoreui/semaphore/db"
	"testing"
	"time"
)

func Test_SetTemplateDescription(t *testing.T) {
	store := CreateTestStore()

	proj, err := store.CreateProject(db.Project{
		Created: time.Now().UTC(),
		Name:    "TestProject",
	})
	if err != nil {
		t.Fatal(err.<PERSON>rror())
	}

	template, err := store.CreateTemplate(db.Template{
		ProjectID: proj.ID,
		Name:      "TestTemplate",
		Playbook:  "test.yml",
	})
	if err != nil {
		t.Fatal(err.Error())
	}

	err = store.SetTemplateDescription(proj.ID, template.ID, "New description")
	if err != nil {
		t.Fatal(err.<PERSON>rror())
	}

	tpl, err := store.GetTemplate(proj.ID, template.ID)
	if err != nil {
		t.Fatal(err.Error())
	}

	if *tpl.Description != "New description" {
		t.Fatalf("expected description to be 'New description', got '%s'", *tpl.Description)
	}
}
