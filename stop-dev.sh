#!/bin/bash

# Semaphore 开发环境停止脚本
# 使用方法: ./stop-dev.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止进程函数
stop_process() {
    local process_name=$1
    local port=$2
    
    log_info "停止 $process_name..."
    
    # 通过端口查找进程
    local pids=$(lsof -ti:$port 2>/dev/null || true)
    
    if [ ! -z "$pids" ]; then
        echo "$pids" | xargs kill -TERM 2>/dev/null || true
        sleep 2
        
        # 检查是否还有进程在运行
        local remaining_pids=$(lsof -ti:$port 2>/dev/null || true)
        if [ ! -z "$remaining_pids" ]; then
            log_warning "$process_name 进程未正常停止，强制终止..."
            echo "$remaining_pids" | xargs kill -9 2>/dev/null || true
        fi
        
        log_success "$process_name 已停止"
    else
        log_info "$process_name 未在运行"
    fi
}

# 停止特定进程名
stop_by_name() {
    local process_pattern=$1
    local description=$2
    
    log_info "停止 $description..."
    
    local pids=$(pgrep -f "$process_pattern" 2>/dev/null || true)
    
    if [ ! -z "$pids" ]; then
        echo "$pids" | xargs kill -TERM 2>/dev/null || true
        sleep 2
        
        # 检查是否还有进程在运行
        local remaining_pids=$(pgrep -f "$process_pattern" 2>/dev/null || true)
        if [ ! -z "$remaining_pids" ]; then
            log_warning "$description 进程未正常停止，强制终止..."
            echo "$remaining_pids" | xargs kill -9 2>/dev/null || true
        fi
        
        log_success "$description 已停止"
    else
        log_info "$description 未在运行"
    fi
}

# 清理日志文件
cleanup_logs() {
    log_info "清理日志文件..."
    
    if [ -f "backend.log" ]; then
        rm -f backend.log
        log_info "已删除 backend.log"
    fi
    
    if [ -f "frontend.log" ]; then
        rm -f frontend.log
        log_info "已删除 frontend.log"
    fi
}

# 显示运行状态
show_status() {
    log_info "检查服务状态..."
    
    # 检查后端
    if lsof -i :3000 &> /dev/null; then
        log_warning "后端服务器仍在运行 (端口 3000)"
    else
        log_success "后端服务器已停止"
    fi
    
    # 检查前端
    if lsof -i :8080 &> /dev/null; then
        log_warning "前端服务器仍在运行 (端口 8080)"
    else
        log_success "前端服务器已停止"
    fi
}

# 主函数
main() {
    log_info "Semaphore 开发环境停止脚本"
    log_info "==============================="
    
    # 停止后端服务器 (端口 3000)
    stop_process "后端服务器" 3000
    
    # 停止前端服务器 (端口 8080)
    stop_process "前端服务器" 8080
    
    # 通过进程名停止可能残留的进程
    stop_by_name "semaphore server" "Semaphore 后端进程"
    stop_by_name "npm run serve" "前端开发服务器"
    stop_by_name "vue-cli-service serve" "Vue CLI 服务"
    
    # 显示状态
    show_status
    
    # 询问是否清理日志
    echo ""
    read -p "是否删除日志文件? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cleanup_logs
    fi
    
    log_success "所有服务已停止！"
    log_info "==============================="
}

# 运行主函数
main
