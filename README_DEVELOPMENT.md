# Semaphore 开发环境快速指南

## 📋 概述

本项目包含了完整的 Semaphore 开发环境，包括：
- **后端**: Go 语言 API 服务器 (端口 3000)
- **前端**: Vue.js + Vuetify Web 界面 (端口 8080)
- **数据库**: BoltDB 嵌入式数据库
- **主题**: 全新的橙色科技风格 UI

## 🚀 快速启动

### 一键启动 (推荐)
```bash
# 启动前后端开发环境
./start-dev.sh

# 只启动后端
./start-dev.sh --backend-only

# 只启动前端
./start-dev.sh --frontend-only
```

### 手动启动
```bash
# 1. 启动后端 (终端1)
./bin/semaphore server --config config.json

# 2. 启动前端 (终端2)
cd web && npm run serve
```

## 🛑 停止服务

```bash
# 停止所有服务
./stop-dev.sh

# 或者按 Ctrl+C (如果使用 start-dev.sh)
```

## 📊 状态检查

```bash
# 检查开发环境状态
./check-status.sh
```

## 🌐 访问地址

- **前端开发**: http://localhost:8080 (热重载)
- **后端 API**: http://localhost:3000
- **API 健康检查**: http://localhost:3000/api/ping
- **生产模式**: http://localhost:3000 (需要先构建前端)

## 📁 项目结构

```
semaphore/
├── bin/semaphore              # 后端二进制文件
├── config.json               # 后端配置
├── database.boltdb           # 数据库文件
├── web/                      # 前端项目
│   ├── src/                  # Vue.js 源码
│   ├── package.json          # 前端依赖
│   └── vue.config.js         # 前端配置
├── start-dev.sh              # 启动脚本
├── stop-dev.sh               # 停止脚本
├── check-status.sh           # 状态检查脚本
├── DEVELOPMENT_GUIDE.md      # 详细开发指南
└── ORANGE_THEME_CHANGES.md   # 橙色主题更改说明
```

## 🎨 新功能 - 橙色科技主题

本次更新包含全新的橙色科技风格主题：

### 视觉特色
- **橙色渐变背景**: 135度线性渐变 (#FF6B35 → #FFA726)
- **科技动画效果**: 浮动圆圈、粒子动画、网格图案
- **玻璃态设计**: 半透明卡片配合模糊效果
- **现代化交互**: 流畅的悬停和焦点状态

### 更新的组件
- ✅ 登录页面 - 科技背景 + 玻璃态卡片
- ✅ 侧边栏 - 橙色渐变背景
- ✅ 数据表格 - 橙色表头样式
- ✅ 按钮 - 橙色渐变效果
- ✅ 输入框 - 橙色边框高亮

## 🛠 开发工具

### 前端开发
```bash
cd web

# 安装依赖
npm install

# 启动开发服务器
npm run serve

# 代码检查
npm run lint

# 自动修复代码格式
npm run lint -- --fix

# 构建生产版本
npm run build
```

### 后端开发
```bash
# 查看版本
./bin/semaphore version

# 查看帮助
./bin/semaphore --help

# 用户管理
./bin/semaphore users add --admin --login admin --name Admin --email admin@localhost

# 数据库迁移
./bin/semaphore migrate --config config.json
```

## 🔧 调试技巧

### 查看日志
```bash
# 后端日志 (如果使用 start-dev.sh)
tail -f backend.log

# 前端日志 (如果使用 start-dev.sh)
tail -f frontend.log

# 实时查看后端输出
./bin/semaphore server --config config.json --log-level DEBUG
```

### API 测试
```bash
# 健康检查
curl http://localhost:3000/api/ping

# 登录测试
curl -H "Content-Type: application/json" \
     -X POST http://localhost:3000/api/auth/login \
     -d '{"auth":"admin","password":"admin"}'
```

### 浏览器调试
- **F12** 打开开发者工具
- **Network** 标签查看 API 请求
- **Console** 标签查看错误信息
- 安装 **Vue DevTools** 扩展

## ❗ 常见问题

### 1. 端口被占用
```bash
# 查看端口占用
lsof -i :3000  # 后端
lsof -i :8080  # 前端

# 释放端口
lsof -ti:3000 | xargs kill -9
```

### 2. 500 错误
- 确保后端服务器正在运行
- 检查 `config.json` 配置
- 查看后端日志输出

### 3. 前端编译错误
```bash
# 清除依赖重新安装
cd web
rm -rf node_modules package-lock.json
npm install
```

### 4. 数据库问题
```bash
# 备份数据库
cp database.boltdb backup_$(date +%Y%m%d_%H%M%S).boltdb

# 重新初始化 (谨慎使用)
./bin/semaphore setup
```

## 📚 文档

- **详细开发指南**: [DEVELOPMENT_GUIDE.md](DEVELOPMENT_GUIDE.md)
- **橙色主题说明**: [ORANGE_THEME_CHANGES.md](ORANGE_THEME_CHANGES.md)
- **官方文档**: https://semaphoreui.com

## 🤝 开发工作流

1. **启动环境**: `./start-dev.sh`
2. **检查状态**: `./check-status.sh`
3. **修改代码**: 前端会自动热重载
4. **测试功能**: 浏览器访问 http://localhost:8080
5. **停止环境**: `./stop-dev.sh` 或 Ctrl+C

## 📞 支持

如果遇到问题：
1. 运行 `./check-status.sh` 检查环境状态
2. 查看相关日志文件
3. 参考 [DEVELOPMENT_GUIDE.md](DEVELOPMENT_GUIDE.md) 详细指南

---

**享受新的橙色科技风格 Semaphore 开发体验！** 🎉
