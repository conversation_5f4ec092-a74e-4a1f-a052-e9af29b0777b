export default {
  checkInterval: 'intervalloDiControllo',
  newCommitCheckInterval: 'Nuovo intervallo di controllo dei commit',
  schedule: 'Programma',
  newSchedule: 'Nuovo programma',
  deleteSchedule: 'Elimina programma',
  editSchedule: 'Modifica programma',
  backup: 'Backup progetto',
  downloadTheProjectBackupFile: 'Scarica il file di backup del progetto.',
  restoreProject: 'Ripristina progetto...',
  incorrectUsrPwd: 'Login o password errati',
  askDeleteUser: 'Vuoi davvero eliminare questo utente?',
  askDeleteTemp: 'Vuoi davvero eliminare questo modello?',
  askDeleteEnv: 'Vuoi davvero eliminare questo ambiente?',
  askDeleteInv: 'Vuoi davvero eliminare questo inventario?',
  ask<PERSON><PERSON><PERSON><PERSON>ey: 'Vuoi davvero eliminare questa chiave?',
  askDeleteRepo: 'Vuoi davvero eliminare questo repository?',
  askDeleteProj: 'Vuoi davvero eliminare questo progetto?',
  askDeleteTMem: 'Vuoi davvero eliminare questo membro del team?',
  askDeleteSchedule: 'Vuoi davvero eliminare questo programma?',
  edit: 'Modifica',
  nnew: 'Nuovo',
  keyFormSshKey: 'Chiave SSH',
  keyFormLoginPassword: 'Accesso con password',
  keyFormNone: 'Nessuno',
  incorrectUrl: 'URL errato',
  username: 'Nome utente',
  username_required: 'Il nome utente è obbligatorio',
  dashboard: 'Cruscotto',
  history: 'Storia',
  activity: 'Attività',
  settings: 'Impostazioni',
  signIn: 'Accedi',
  password: 'Password',
  changePassword: 'Cambia password',
  editUser: 'Modifica utente',
  newProject: 'Nuovo progetto',
  close: 'Chiudi',
  newProject2: 'Nuovo progetto...',
  demoMode: 'MODALITÀ DEMO',
  task: 'Compito #{expr}',
  youCanRunAnyTasks: 'Puoi eseguire qualsiasi compito',
  youHaveReadonlyAccess: 'Hai accesso in sola lettura',
  taskTemplates: 'Modelli di compito',
  inventory: 'Inventario',
  environment: 'Gruppi di variabili',
  keyStore: 'Negozio chiavi',
  repositories: 'Repository',
  darkMode: 'Modalità scura',
  team: 'Team',
  users: 'Utenti',
  editAccount: 'Modifica account',
  signOut: 'Disconnetti',
  error: 'Errore',
  refreshPage: 'Aggiorna pagina',
  relogin: 'Riconnetti',
  howToFixSigninIssues: 'Come risolvere i problemi di accesso',
  firstlyYouNeedAccessToTheServerWhereSemaphoreRunni: 'In primo luogo, è necessario avere accesso al server in cui è in esecuzione Semaphore.',
  executeTheFollowingCommandOnTheServerToSeeExisting: 'Esegui il seguente comando sul server per vedere gli utenti esistenti:',
  semaphoreUserList: 'semaphore user list',
  youCanChangePasswordOfExistingUser: 'Puoi cambiare la password dell\'utente esistente:',
  semaphoreUserChangebyloginLoginUser123Password: 'semaphore user change-by-login --login user123 --password {makePasswordExample}',
  orCreateNewAdminUser: 'O crea un nuovo utente admin:',
  close2: 'Chiudi',
  semaphore: 'SEMAFORO',
  dontHaveAccountOrCantSignIn: 'Non hai un account o non riesci ad accedere?',
  password2: 'Password',
  cancel: 'Annulla',
  noViews: 'Nessuna vista',
  addView: 'Aggiungi vista',
  editEnvironment: 'Modifica gruppo di variabili',
  deleteEnvironment: 'Elimina gruppo di variabili',
  newEnvironment: 'Nuovo gruppo',
  environmentName: 'Nome gruppo',
  extraVariables: 'Variabili extra',
  enterExtraVariablesJson: 'Inserisci variabili extra JSON...',
  environmentVariables: 'Variabili di ambiente',
  enterEnvJson: 'Inserisci JSON ambiente...',
  environmentAndExtraVariablesMustBeValidJsonExample: 'Le variabili di ambiente e le variabili extra devono essere JSON validi. Esempio:',
  dashboard2: 'Cruscotto',
  ansibleSemaphore: 'Interfaccia utente Semaphore',
  wereSorryButHtmlwebpackpluginoptionstitleDoesntWor: 'Ci dispiace, ma <%= htmlWebpackPlugin.options.title %> non funziona correttamente senza JavaScript abilitato. Abilitalo per continuare.',
  deleteInventory: 'Elimina inventario',
  newInventory: 'Nuovo inventario',
  name: 'Nome',
  userCredentials: 'Credenziali utente',
  sudoCredentialsOptional: 'Credenziali Sudo (Opzionale)',
  type: 'Tipo',
  pathToInventoryFile: 'Percorso del file di inventario',
  enterInventory: 'Inserisci inventario...',
  staticInventoryExample: 'Esempio di inventario statico:',
  staticYamlInventoryExample: 'Esempio di inventario YAML statico:',
  keyName: 'Nome chiave',
  loginOptional: 'Accesso (Opzionale)',
  usernameOptional: 'Nome utente (Opzionale)',
  privateKey: 'Chiave privata',
  override: 'Sovrascrivi',
  useThisTypeOfKeyForHttpsRepositoriesAndForPlaybook: 'Usa questo tipo di chiave per i repository HTTPS e per i playbook che utilizzano connessioni non SSH.',
  deleteKey: 'Elimina chiave',
  newKey: 'Nuova chiave',
  create: 'Crea',
  newTask: 'Nuovo compito',
  cantDeleteThe: 'Impossibile eliminare {objectTitle}',
  theCantBeDeletedBecauseItUsedByTheResourcesBelow: '{objectTitle} non può essere eliminato perché è utilizzato dalle risorse sottostanti',
  projectName: 'Nome progetto',
  allowAlertsForThisProject: 'Consenti avvisi per questo progetto',
  telegramChatIdOptional: 'ID chat Telegram (Opzionale)',
  maxNumberOfParallelTasksOptional: 'Numero massimo di compiti paralleli (Opzionale)',
  deleteRepository: 'Elimina repository',
  newRepository: 'Nuovo repository',
  urlOrPath: 'URL o percorso',
  absPath: 'percorso ass.',
  branch: 'Ramo',
  accessKey: 'Chiave di accesso',
  credentialsToAccessToTheGitRepositoryItShouldBe: 'Credenziali per accedere al repository Git. Dovrebbe essere:',
  ifYouUseGitOrSshUrl: 'se utilizzi l\'URL Git o SSH.',
  ifYouUseHttpsOrFileUrl: 'se utilizzi l\'URL HTTPS o file.',
  none: 'Nessuno',
  ssh: 'SSH',
  deleteProject: 'Elimina progetto',
  save: 'Salva',
  deleteProject2: 'Elimina progetto',
  onceYouDeleteAProjectThereIsNoGoingBackPleaseBeCer: 'Una volta eliminato un progetto, non c\'è modo di tornare indietro. Per favore, sii certo.',

  clear_cache: 'Pulisci cache',
  clear_cache_message: 'Elimina tutti i file di cache relativi a questo progetto. Questa azione è irreversibile.',

  name2: 'Nome *',
  title: 'Titolo *',
  description: 'Descrizione',
  required: 'Obbligatorio',
  key: '{expr}',
  surveyVariables: 'Variabili del sondaggio',
  addVariable: 'Aggiungi variabile',
  vaultName: 'ID Vault (opzionale)',
  vaultNameDefault: 'Può esistere solo un nome `default` (vuoto)',
  vaultNameUnique: 'Deve essere unico',
  vaultTypePassword: 'Password',
  vaultTypeScript: 'Script client',
  vaultScript: 'Percorso script',
  vaultScriptRequired: 'Il percorso dello script è obbligatorio',
  vaultScriptClientRequired: 'Il percorso dello script deve terminare con \'-client\' e un\'estensione',
  vaults: 'Vaults',
  vaultAdd: 'Aggiungi Vault',
  vaultRequired: 'La password del Vault è obbligatoria',
  columns: 'Colonne',
  buildVersion: 'Versione di build',
  messageOptional: 'Messaggio (Opzionale)',
  debug: 'Debug',
  dryRun: 'Esecuzione simulata',
  diff: 'Differenza',
  advanced: 'Avanzato',
  hide: 'Nascondi',
  pleaseAllowOverridingCliArgumentInTaskTemplateSett: 'Per consentire la sovrascrittura dell\'argomento CLI nelle impostazioni del modello di compito',
  cliArgsJsonArrayExampleIMyinventoryshPrivatekeythe: 'Argomenti CLI (array JSON). Esempio: [ "-i", "@myinventory.sh", "--private-key=/there/id_rsa", "-vvvv" ]',
  started: 'Iniziato',
  author: 'Autore',
  duration: 'Durata',
  stop: 'Ferma',
  forceStop: 'Ferma forzatamente',
  raw_log: 'Log grezzo',

  confirmTask: 'Conferma',
  deleteTeamMember: 'Elimina membro del team',
  team2: 'Team',
  newTeamMember: 'Nuovo membro del team',
  user: 'Utente',
  administrator: 'Amministratore',
  definesStartVersionOfYourArtifactEachRunIncrements: 'Definisce la versione di partenza del tuo artefatto. Ogni esecuzione incrementa la versione dell\'artefatto.',
  forMoreInformationAboutBuildingSeeThe: 'Per ulteriori informazioni sulla costruzione, vedere il',
  taskTemplateReference: 'Riferimento modello di compito',
  definesWhatArtifactShouldBeDeployedWhenTheTaskRun: 'Definisce quale artefatto deve essere distribuito quando il compito viene eseguito.',
  forMoreInformationAboutDeployingSeeThe: 'Per ulteriori informazioni sulla distribuzione, vedere il',
  taskTemplateReference2: 'Riferimento modello di compito',
  definesAutorunSchedule: 'Definisce il programma di esecuzione automatica.',
  forMoreInformationAboutCronSeeThe: 'Per ulteriori informazioni su cron, vedere il',
  cronExpressionFormatReference: 'Riferimento formato espressione cron',
  startVersion: 'Versione di partenza',
  example000: 'Esempio: 0.0.0',
  buildTemplate: 'Modello di build',
  autorun: 'Esecuzione automatica',
  playbookFilename: 'Percorso del file playbook *',
  exampleSiteyml: 'Esempio: deploy/site.yml',
  inventory2: 'Inventario *',
  repository: 'Repository',
  environment3: 'Gruppo di variabili *',
  vaultPassword: 'Password del Vault',
  vaultPassword2: 'Password del Vault',
  view: 'Vista',
  cron: 'Cron',
  iWantToRunATaskByTheCronOnlyForForNewCommitsOfSome: 'Voglio eseguire un compito tramite cron solo per nuovi commit di un repository',
  repository2: 'Repository',
  cronChecksNewCommitBeforeRun: 'Cron controlla nuovi commit prima di eseguire',
  readThe: 'Leggi il',
  toLearnMoreAboutCron: 'per saperne di più su Cron.',
  suppressSuccessAlerts: 'Sopprimi avvisi di successo',
  cliArgsJsonArrayExampleIMyinventoryshPrivatekeythe2: 'Argomenti CLI (array JSON). Esempio: [ "-i", "@myinventory.sh", "--private-key=/there/id_rsa", "-vvvv" ]',
  allowCliArgsInTask: 'Argomenti CLI',
  docs: 'documenti',
  editViews: 'Modifica viste',
  newTemplate: 'Nuovo modello',
  taskTemplates2: 'Modelli di compito',
  all: 'Tutti',
  notLaunched: 'Non avviato',
  by: 'da {user_name}',
  editTemplate: 'Modifica modello',
  newTemplate2: 'Nuovo modello',
  deleteTemplate: 'Elimina modello',
  playbook: 'Playbook',
  email: 'Email',
  adminUser: 'Utente admin',
  sendAlerts: 'Invia avvisi',
  deleteUser: 'Elimina utente',
  newUser: 'Nuovo utente',
  re: 'Re{getActionButtonTitle}',
  teamMember: '{expr} Membro del team',
  taskId: 'ID compito',
  version: 'Versione',
  status: 'Stato',
  start: 'Inizio',
  actions: 'Azioni',
  alert: 'Avviso',
  admin: 'Admin',
  role: 'Ruolo',
  external: 'Esterno',
  time: 'Tempo',
  path: 'Percorso',
  gitUrl: 'URL Git',
  sshKey: 'Chiave SSH',
  lastTask: 'Ultimo compito',
  task2: 'Compito',
  build: 'Costruisci',
  deploy: 'Distribuisci',
  run: 'Esegui',
  add: 'Aggiungi',
  password_required: 'La password è obbligatoria',
  name_required: 'Il nome è obbligatorio',
  user_credentials_required: 'Le credenziali dell\'utente sono obbligatorie',
  type_required: 'Il tipo è obbligatorio',
  path_required: 'Il percorso del file di inventario è obbligatorio',
  private_key_required: 'La chiave privata è obbligatoria',
  project_name_required: 'Il nome del progetto è obbligatorio',
  repository_required: 'Il repository è obbligatorio',
  branch_required: 'Il ramo è obbligatorio',
  key_required: 'La chiave è obbligatoria',
  user_required: 'L\'utente è obbligatorio',
  build_version_required: 'La versione di build è obbligatoria',
  title_required: 'Il titolo è obbligatorio',
  isRequired: 'è obbligatorio',
  mustBeInteger: 'Deve essere un intero',
  mustBe0OrGreater: 'Deve essere 0 o maggiore',
  start_version_required: 'La versione di partenza è obbligatoria',
  playbook_filename_required: 'Il nome del file playbook è obbligatorio',
  inventory_required: 'L\'inventario è obbligatorio',
  environment_required: 'L\'ambiente è obbligatorio',
  email_required: 'L\'email è obbligatoria',
  build_template_required: 'Il modello di build è obbligatorio',
  Task: 'Compito',
  Build: 'Costruisci',
  Deploy: 'Distribuisci',
  Run: 'Esegui',
  ReBuild: 'Ricostruisci',
  ReDeploy: 'Ridispiega',
  ReRun: 'Riesegui',
  CreateDemoProject: 'Crea progetto demo',
  LeaveProject: 'Lascia progetto',
  integration: 'Integrazione',
  integrations: 'Integrazioni',
  NewIntegration: 'Nuova integrazione',
  EditIntegration: 'Modifica integrazione',
  DeleteIntegration: 'Elimina integrazione',
  DeleteIntegrationMsg: 'Sei sicuro di voler eliminare questa integrazione?',
  AddAlias: 'Aggiungi alias',
  LoadAlias: 'Caricamento alias...',
  globalAlias: 'Usa alias progetto',
  matcher: 'Corrispondente',
  matchType: 'Tipo di corrispondenza',
  newMatcher: 'Nuovo corrispondente',
  matchMethod: 'Metodo di confronto',
  matchBodyDataType: 'Tipo di dati del corpo',
  extractValue: 'Estrai valore',
  newExtractedValue: 'Nuovo valore estratto',
  extractedValueSource: 'Fonte valore',
  matchKey: 'Chiave',
  matchValue: 'Valore',
  matchOn: 'Corrispondenza su',
  runners: 'Esecutori',
  newRunner: 'Nuovo esecutore',
  enabled: 'Abilitato',
  scheduleNextRun: 'Prossima esecuzione',
  maxNumberOfParallelTasks: 'Numero massimo di compiti paralleli',
  runnerUsage: 'Utilizzo:',
  runnerDockerCommand: 'Docker:',
  runnerToken: 'Token:',
  editRunner: 'Modifica esecutore',
  deleteRunner: 'Elimina esecutore',
  newRunnerToken: 'Nuovo esecutore',
  askDeleteRunner: 'Vuoi davvero eliminare l\'esecutore {runner}?',
  project_runners_only_pro: 'Gli esecutori a livello di progetto sono disponibili solo nella versione <b>PRO</b>.',
  foss_runners_limited: 'La versione open-source ha funzionalità limitate; la funzionalità completa è nella versione <b>PRO</b>.',
  learn_more_about_pro: 'Scopri di più',

  projectRestoreResult: 'Risultati del ripristino del progetto',
  projectWithNameRestored: 'Progetto {projectName} ripristinato con successo.',
  pleaseUpdateAccessKeys: 'Si prega di aggiornarli prima di eseguire i compiti.',
  emptyKeysRestored: '{emptyKeys} chiavi vuote aggiunte.',
  template: 'Modello',
  aliasUrlCopied: 'L\'URL dell\'alias è stato copiato negli appunti.',
  yes: 'Sì',
  activeTasks: 'Compiti attivi',
  taskLocation: 'Posizione',
  empty: 'Vuoto',
  noValues: 'Nessun valore',
  addArg: 'Aggiungi argomento',

  status_success: 'Successo',
  status_failed: 'Fallito',
  status_stopped: 'Fermato',

  api_tokens: 'Token API',

  // Terraform/OpenTofu
  auto_approve: 'Approvazione automatica',

  // Ansible
  tag: 'Tag',
  tag_required: 'Il tag è obbligatorio',
  allowInventoryInTask: 'Inventario',
  allowLimitInTask: 'Limite',
  addLimit: 'Aggiungi limite',
  allowDebug: 'Debug',
  addTag: 'Aggiungi tag',
  skipTags: 'Salta tag',
  addSkippedTag: 'Aggiungi tag saltato',
  tags: 'Tag',
  limit: 'Limite',

  runner_tag: 'Tag esecutore',
  task_prompts: 'Richieste',
  template_advanced: 'Opzioni avanzate',
  template_app_options: 'Opzioni {app}',
  template_app_prompts: 'Richieste {app}',
  general_settings: 'Generale',
  danger_zone_settings: 'Zona pericolosa',
  project_stats: 'Statistiche',
  allow_override_branch: 'Ramo',
  template_common_options: 'Opzioni comuni',
};
