export default {
  checkInterval: 'інтервал перевірки',
  newCommitCheckInterval: 'інтервал перевірки нового коміта',
  schedule: 'Розклад',
  newSchedule: 'Новий розклад',
  deleteSchedule: 'Видалити розклад',
  editSchedule: 'Редагувати розклад',
  backup: 'Резервне копіювання проєкту',
  downloadTheProjectBackupFile: 'Завантажити файл резервної копії проєкту (у форматі JSON)',
  restoreProject: 'Відновити проєкт...',
  incorrectUsrPwd: 'Неправильне ім\'я користувача або пароль',
  askDeleteUser: 'Ви дійсно хочете видалити цього користувача?',
  askDeleteTemp: 'Ви дійсно хочете видалити цей шаблон?',
  askDeleteEnv: 'Ви дійсно хочете видалити це середовище?',
  askDeleteInv: 'Ви дійсно хочете видалити цей інвентар?',
  askDeleteKey: 'Ви дійсно хочете видалити цей ключ?',
  askDeleteRepo: 'Ви дійсно хочете видалити цей репозиторій?',
  askDeleteProj: 'Ви дійсно хочете видалити цей проєкт?',
  askDeleteTMem: 'Ви дійсно хочете видалити цього члена команди?',
  askDeleteSchedule: 'Ви дійсно хочете видалити цей розклад?',
  edit: 'Редагувати',
  nnew: 'Новий',
  keyFormSshKey: 'SSH-ключ',
  keyFormLoginPassword: 'Логін з паролем',
  keyFormNone: 'Немає',
  incorrectUrl: 'Неправильний URL',
  username: 'Ім\'я користувача',
  username_required: 'Ім\'я користувача обов\'язкове',
  dashboard: 'Панель керування',
  history: 'Історія',
  activity: 'Активність',
  settings: 'Налаштування',
  signIn: 'Увійти',
  password: 'Пароль',
  changePassword: 'Змінити пароль',
  editUser: 'Редагувати користувача',
  newProject: 'Новий проєкт',
  close: 'Закрити',
  newProject2: 'Новий проєкт...',
  demoMode: 'ДЕМО РЕЖИМ',
  task: 'Завдання #{expr}',
  youCanRunAnyTasks: 'Ви можете виконувати будь-які завдання',
  youHaveReadonlyAccess: 'У вас доступ тільки для читання',
  taskTemplates: 'Шаблони завдань',
  inventory: 'Інвентар',
  environment: 'Змінні середовища',
  keyStore: 'Сховище ключів',
  repositories: 'Репозиторії',
  darkMode: 'Темний режим',
  team: 'Команда',
  users: 'Користувачі',
  editAccount: 'Редагувати обліковий запис',
  signOut: 'Вийти',
  error: 'Помилка',
  refreshPage: 'Оновити сторінку',
  relogin: 'Знову увійти',
  howToFixSigninIssues: 'Як виправити проблеми входу',
  firstlyYouNeedAccessToTheServerWhereSemaphoreRunni: 'Спочатку вам потрібно мати доступ до сервера, де працює Семафор.',
  executeTheFollowingCommandOnTheServerToSeeExisting: 'Виконайте наступну команду на сервері, щоб побачити існуючих користувачів:',
  semaphoreUserList: 'список користувачів semaphore',
  youCanChangePasswordOfExistingUser: 'Ви можете змінити пароль наявного користувача:',
  semaphoreUserChangebyloginLoginUser123Password: 'semaphore user change-by-login --login user123 --password {makePasswordExample}',
  orCreateNewAdminUser: 'Або створіть нового адміністратора:',
  close2: 'Закрити',
  semaphore: 'СЕМАФОР',
  dontHaveAccountOrCantSignIn: 'Немає облікового запису або не можете увійти?',
  password2: 'Пароль',
  cancel: 'Скасувати',
  noViews: 'Немає переглядів',
  addView: 'Додати перегляд',
  editEnvironment: 'Редагувати середовище',
  deleteEnvironment: 'Видалити середовище',
  newEnvironment: 'Нове середовище',
  environmentName: 'Назва середовища',
  extraVariables: 'Додаткові змінні',
  enterExtraVariablesJson: 'Введіть додаткові змінні у форматі JSON...',
  environmentVariables: 'Змінні середовища',
  enterEnvJson: 'Введіть змінні середовища у форматі JSON...',
  environmentAndExtraVariablesMustBeValidJsonExample: 'Змінні середовища та додаткові змінні мають бути у форматі дійсного JSON. Наприклад:',
  dashboard2: 'Панель керування',
  ansibleSemaphore: 'Інтерфейс Семафора',
  wereSorryButHtmlwebpackpluginoptionstitleDoesntWor: 'Вибачте, але <%= htmlWebpackPlugin.options.title %> не працює належним чином без увімкненого JavaScript. Будь ласка, увімкніть його для продовження.',
  deleteInventory: 'Видалити інвентар',
  newInventory: 'Новий інвентар',
  name: 'Назва',
  userCredentials: 'Облікові дані користувача',
  sudoCredentialsOptional: 'Облікові дані Sudo (не обов\'язково)',
  type: 'Тип',
  pathToInventoryFile: 'Шлях до файлу інвентарю',
  enterInventory: 'Введіть інвентар...',
  staticInventoryExample: 'Приклад статичного інвентарю:',
  staticYamlInventoryExample: 'Приклад статичного YAML інвентарю:',
  keyName: 'Назва ключа',
  loginOptional: 'Логін (не обов\'язково)',
  usernameOptional: 'Ім\'я користувача (не обов\'язково)',
  privateKey: 'Приватний ключ',
  override: 'Замінити',
  useThisTypeOfKeyForHttpsRepositoriesAndForPlaybook: 'Використовуйте цей тип ключа для HTTPS репозиторіїв та для плейбуків, якщо немає можливості використовувати SSH з\'єднання.',
  deleteKey: 'Видалити ключ',
  newKey: 'Новий ключ',
  create: 'Створити',
  newTask: 'Нове завдання',
  cantDeleteThe: 'Не можу видалити {objectTitle}',
  theCantBeDeletedBecauseItUsedByTheResourcesBelow: '{objectTitle} не може бути видалений, оскільки він використовується нижчезазначеними ресурсами',
  projectName: 'Назва проєкту',
  allowAlertsForThisProject: 'Дозволити сповіщення для цього проєкту',
  telegramChatIdOptional: 'Telegram Chat ID (опціонально)',
  maxNumberOfParallelTasksOptional: 'Максимальна кількість паралельних завдань (опціонально)',
  deleteRepository: 'Видалити репозиторій',
  newRepository: 'Новий репозиторій',
  urlOrPath: 'URL або шлях',
  absPath: 'абс. шлях',
  branch: 'Гілка',
  accessKey: 'Ключ доступу',
  credentialsToAccessToTheGitRepositoryItShouldBe: 'Облікові дані для доступу до Git репозиторію. Це має бути:',
  ifYouUseGitOrSshUrl: 'якщо ви використовуєте Git або SSH URL.',
  ifYouUseHttpsOrFileUrl: 'якщо ви використовуєте HTTPS або URL файлу.',
  none: 'Немає',
  ssh: 'SSH',
  deleteProject: 'Видалити проєкт',
  save: 'Зберегти',
  deleteProject2: 'Видалити проєкт',
  onceYouDeleteAProjectThereIsNoGoingBackPleaseBeCer: 'Після видалення проєкту не буде можливості повернутись. Будь ласка, будьте впевнені.',
  name2: 'Ім\'я *',
  title: 'Назва *',
  description: 'Опис',
  required: 'Обов\'язкове',
  key: '{expr}',
  surveyVariables: 'Змінні опитування',
  addVariable: 'Додати змінну',
  vaultName: 'Vault ID (ім\'я)',
  vaultNameDefault: 'Може існувати тільки одне `default` (порожнє) ім\'я',
  vaultNameUnique: 'Повинно бути унікальним',
  vaults: 'Сховища',
  vaultAdd: 'Додати сховище',
  vaultRequired: 'Необхідний пароль сховища',
  columns: 'Колонки',
  buildVersion: 'Версія збірки',
  messageOptional: 'Повідомлення (опціонально)',
  debug: 'Пошук помилок',
  dryRun: 'Імітаційний запуск',
  diff: 'Різниця',
  advanced: 'Розширені налаштування',
  hide: 'Приховати',
  pleaseAllowOverridingCliArgumentInTaskTemplateSett: 'Дозволити переопределение аргументів CLI в налаштуваннях шаблону завдання',
  cliArgsJsonArrayExampleIMyinventoryshPrivatekeythe: 'Аргументи CLI (JSON масив). Приклад: [ "-i", "@myinventory.sh", "--private-key=/there/id_rsa", "-vvvv" ]',
  started: 'Розпочато',
  author: 'Автор',
  duration: 'Тривалість',
  stop: 'Зупинити',
  forceStop: 'Примусово зупинити',
  confirmTask: 'Підтвердити',
  deleteTeamMember: 'Видалити члена команди',
  team2: 'Команда',
  newTeamMember: 'Новий член команди',
  user: 'Користувач',
  administrator: 'Адміністратор',
  definesStartVersionOfYourArtifactEachRunIncrements: 'Визначає початкову версію вашого артефакту. Кожен запуск збільшує версію артефакту.',
  forMoreInformationAboutBuildingSeeThe: 'Для отримання додаткової інформації про збірку дивіться',
  taskTemplateReference: 'Довідник шаблонів завдань',
  definesWhatArtifactShouldBeDeployedWhenTheTaskRun: 'Визначає, який саме артефакт повинен бути розгорнутий під час виконання завдання.',
  forMoreInformationAboutDeployingSeeThe: 'Для отримання додаткової інформації про розгортання дивіться',
  taskTemplateReference2: 'Довідник шаблонів завдань',
  definesAutorunSchedule: 'Визначає розклад автоматичного запуску.',
  forMoreInformationAboutCronSeeThe: 'Для отримання додаткової інформації про планувальник дивіться',
  cronExpressionFormatReference: 'Довідник форматів виразів планувальника',
  startVersion: 'Початкова версія',
  example000: 'Приклад: 0.0.0',
  buildTemplate: 'Шаблон збірки',
  autorun: 'Автоматичний запуск',
  playbookFilename: 'Назва файлу плейбуку *',
  exampleSiteyml: 'Приклад: deploy/site.yml',
  inventory2: 'Інвентар *',
  repository: 'Репозиторій',
  environment3: 'Середовище *',
  vaultPassword: 'Пароль сховища',
  vaultPassword2: 'Пароль сховища',
  view: 'Перегляд',
  cron: 'Планувальник',
  iWantToRunATaskByTheCronOnlyForForNewCommitsOfSome: 'Я хочу запускати завдання за розкладом тільки для нових комітів певного репозиторію',
  repository2: 'Репозиторій',
  cronChecksNewCommitBeforeRun: 'Планувальник перевіряє новий коміт перед запуском',
  readThe: 'Читати',
  toLearnMoreAboutCron: 'щоб дізнатися більше про планувальник.',
  suppressSuccessAlerts: 'Пригнічувати сповіщення про успіх',
  cliArgsJsonArrayExampleIMyinventoryshPrivatekeythe2: 'Аргументи CLI (у вигляді JSON масиву). Приклад: [ "-i", "@myinventory.sh", "--private-key=/there/id_rsa", "-vvvv" ]',
  allowCliArgsInTask: 'Дозволити аргументи CLI в завданні',
  docs: 'документація',
  editViews: 'Редагувати перегляди',
  newTemplate: 'Новий шаблон',
  taskTemplates2: 'Шаблони завдань',
  all: 'Усі',
  notLaunched: 'Не запущено',
  by: 'від {user_name}',
  editTemplate: 'Редагувати шаблон',
  newTemplate2: 'Новий шаблон',
  deleteTemplate: 'Видалити шаблон',
  playbook: 'Плейбук',
  email: 'Ел. пошта',
  adminUser: 'Адміністратор',
  sendAlerts: 'Надіслати сповіщення',
  deleteUser: 'Видалити користувача',
  newUser: 'Новий користувач',
  re: 'Re{getActionButtonTitle}',
  teamMember: '{expr} член команди',
  taskId: 'ID завдання',
  version: 'Версія',
  status: 'Статус',
  start: 'Початок',
  actions: 'Дії',
  alert: 'Оповіщення',
  admin: 'Адмін',
  role: 'Роль',
  external: 'Зовнішній',
  time: 'Час',
  path: 'Шлях',
  gitUrl: 'Git URL',
  sshKey: 'SSH ключ',
  lastTask: 'Останнє завдання',
  task2: 'Завдання',
  build: 'Збірка',
  deploy: 'Розгортання',
  run: 'Запуск',
  add: 'Додати',
  password_required: 'Пароль обов\'язковий',
  name_required: 'Ім\'я обов\'язкове',
  user_credentials_required: 'Необхідні облікові дані користувача',
  type_required: 'Тип обов\'язковий',
  path_required: 'Шлях до файлу інвентаря обов\'язковий',
  private_key_required: 'Приватний ключ обов\'язковий',
  project_name_required: 'Назва проєкту обов\'язкова',
  repository_required: 'Репозиторій обов\'язковий',
  branch_required: 'Гілка обов\'язкова',
  key_required: 'Ключ обов\'язковий',
  user_required: 'Користувач обов\'язковий',
  build_version_required: 'Версія збірки обов\'язкова',
  title_required: 'Заголовок обов\'язковий',
  isRequired: 'обов\'язковий',
  mustBeInteger: 'Має бути цілим числом',
  mustBe0OrGreater: 'Має бути 0 або більше',
  start_version_required: 'Початкова версія обов\'язкова',
  playbook_filename_required: 'Назва файлу плейбука обов\'язкова',
  inventory_required: 'Інвентар обов\'язковий',
  environment_required: 'Середовище обов\'язкове',
  email_required: 'Електронна пошта обов\'язкова',
  build_template_required: 'Шаблон збірки обов\'язковий',
  Task: 'Завдання',
  Build: 'Збірка',
  Deploy: 'Розгортання',
  Run: 'Запуск',
  ReBuild: 'Перезбірка',
  ReDeploy: 'Перерозгортання',
  ReRun: 'Перезапуск',
  CreateDemoProject: 'Створити демонстраційний проєкт',
  LeaveProject: 'Вийти з проєкту',
  integration: 'Інтеграція',
  integrations: 'Інтеграції',
  NewIntegration: 'Нова інтеграція',
  EditIntegration: 'Редагувати інтеграцію',
  DeleteIntegration: 'Видалити інтеграцію',
  DeleteIntegrationMsg: 'Ви впевнені, що хочете видалити цю інтеграцію?',
  AddAlias: 'Додати псевдонім',
  LoadAlias: 'Завантаження псевдонімів...',
  globalAlias: 'Доступний за проєктом і глобальним псевдонімом',
  matcher: 'Відповідач',
  matchType: 'Тип відповідності',
  newMatcher: 'Новий порівнювач',
  matchMethod: 'Метод порівняння',
  matchBodyDataType: 'Тип даних тіла',
  extractValue: 'Витягти значення',
  newExtractedValue: 'Нове витягнуте значення',
  extractedValueSource: 'Джерело значення',
  matchKey: 'Ключ',
  matchValue: 'Значення',
  matchOn: 'Відповідає на',
  runners: 'Виконавці',
  newRunner: 'Новий виконавець',
  enabled: 'Включено',
  scheduleNextRun: 'Наступний запуск',
  maxNumberOfParallelTasks: 'Максимальна кількість паралельних задач',
  runnerUsage: 'Використання:',
  runnerDockerCommand: 'Docker:',
  runnerToken: 'Жетон:',
  editRunner: 'Редагувати виконавця',
  deleteRunner: 'Видалити виконавця',
  newRunnerToken: 'Новий виконавець',
  askDeleteRunner: 'Ви дійсно хочете видалити виконавця {runner}?',
  projectRestoreResult: 'Результати відновлення проєкту',
  projectWithNameRestored: 'Проєкт {projectName} успішно відновлено.',
  pleaseUpdateAccessKeys: 'Будь ласка, оновіть їх перед запуском завдань.',
  emptyKeysRestored: '{emptyKeys} порожніх ключів додано.',
  template: 'Шаблон',
  aliasUrlCopied: 'URL псевдоніма скопійований у буфер обміну.',
  yes: 'Так',
  activeTasks: 'Активні завдання',
  taskLocation: 'Розташування',
  empty: 'Порожній',
  noValues: 'Немає значень',
  addArg: 'Додати аргумент',
  status_success: 'Успіх',
  status_failed: 'Невдача',
  status_stopped: 'Зупинено',
};
