export default {
  checkInterval: '检查间隔',
  newCommitCheckInterval: '新提交检查间隔',
  schedule: '计划',
  newSchedule: '新计划',
  deleteSchedule: '删除计划',
  editSchedule: '编辑计划',
  backup: '备份项目',
  downloadTheProjectBackupFile: '下载项目备份文件。',
  restoreProject: '恢复项目...',
  incorrectUsrPwd: '登录或密码不正确',
  askDeleteUser: '您真的想删除此用户吗？',
  askDeleteTemp: '您真的想删除此模板吗？',
  askDeleteEnv: '您真的想删除此环境吗？',
  askDeleteInv: '您真的想删除此库存吗？',
  askDeleteKey: '您真的想删除此密钥吗？',
  askDeleteRepo: '您真的想删除此仓库吗？',
  askDeleteProj: '您真的想删除此项目吗？',
  askDeleteTMem: '您真的想删除此团队成员吗？',
  askDeleteSchedule: '您真的想删除此计划吗？',
  edit: '编辑',
  nnew: '新建',
  keyFormSshKey: 'SSH 密钥',
  keyFormLoginPassword: '使用密码登录',
  keyFormNone: '无',
  incorrectUrl: '不正确的 URL',
  username: '用户名',
  username_required: '用户名是必需的',
  dashboard: '仪表板',
  history: '历史',
  activity: '活动',
  settings: '设置',
  signIn: '登录',
  password: '密码',
  changePassword: '更改密码',
  editUser: '编辑用户',
  newProject: '新项目',
  close: '关闭',
  newProject2: '新项目...',
  demoMode: '演示模式',
  task: '任务 #{expr}',
  youCanRunAnyTasks: '您可以运行任何任务',
  youHaveReadonlyAccess: '您具有只读访问权限',
  taskTemplates: '任务模板',
  inventory: '库存',
  environment: '变量组',
  keyStore: '密钥存储',
  repositories: '仓库',
  darkMode: '黑暗模式',
  team: '团队',
  users: '用户',
  editAccount: '编辑账户',
  signOut: '登出',
  error: '错误',
  refreshPage: '刷新页面',
  relogin: '重新登录',
  howToFixSigninIssues: '如何解决登录问题',
  firstlyYouNeedAccessToTheServerWhereSemaphoreRunni: '首先，您需要访问运行 Semaphore 的服务器。',
  executeTheFollowingCommandOnTheServerToSeeExisting: '在服务器上执行以下命令以查看现有用户：',
  semaphoreUserList: 'semaphore user list',
  youCanChangePasswordOfExistingUser: '您可以更改现有用户的密码：',
  semaphoreUserChangebyloginLoginUser123Password: 'semaphore user change-by-login --login user123 --password {makePasswordExample}',
  orCreateNewAdminUser: '或创建新管理员用户：',
  close2: '关闭',
  semaphore: '信号灯',
  dontHaveAccountOrCantSignIn: '没有账户或无法登录？',
  password2: '密码',
  cancel: '取消',
  noViews: '没有视图',
  addView: '添加视图',
  editEnvironment: '编辑变量组',
  deleteEnvironment: '删除变量组',
  newEnvironment: '新组',
  environmentName: '组名',
  extraVariables: '额外变量',
  enterExtraVariablesJson: '输入额外变量 JSON...',
  environmentVariables: '环境变量',
  enterEnvJson: '输入环境 JSON...',
  environmentAndExtraVariablesMustBeValidJsonExample: '环境和额外变量必须是有效的 JSON。示例：',
  dashboard2: '仪表板',
  ansibleSemaphore: '信号灯 UI',
  wereSorryButHtmlwebpackpluginoptionstitleDoesntWor: '我们很抱歉，但 <%= htmlWebpackPlugin.options.title %> 在没有启用 JavaScript 的情况下无法正常工作。请启用它以继续。',
  deleteInventory: '删除库存',
  newInventory: '新库存',
  name: '名称',
  userCredentials: '用户凭据',
  sudoCredentialsOptional: 'Sudo 凭据（可选）',
  type: '类型',
  pathToInventoryFile: '库存文件路径',
  enterInventory: '输入库存...',
  staticInventoryExample: '静态库存示例：',
  staticYamlInventoryExample: '静态 YAML 库存示例：',
  keyName: '密钥名称',
  loginOptional: '登录（可选）',
  usernameOptional: '用户名（可选）',
  privateKey: '私钥',
  override: '覆盖',
  useThisTypeOfKeyForHttpsRepositoriesAndForPlaybook: '使用此类型的密钥用于 HTTPS 仓库和使用非 SSH 连接的剧本。',
  deleteKey: '删除密钥',
  newKey: '新密钥',
  create: '创建',
  newTask: '新任务',
  cantDeleteThe: '无法删除 {objectTitle}',
  theCantBeDeletedBecauseItUsedByTheResourcesBelow: '{objectTitle} 无法删除，因为它被以下资源使用',
  projectName: '项目名称',
  allowAlertsForThisProject: '允许此项目的警报',
  telegramChatIdOptional: 'Telegram 聊天 ID（可选）',
  maxNumberOfParallelTasksOptional: '最大并行任务数（可选）',
  deleteRepository: '删除仓库',
  newRepository: '新仓库',
  urlOrPath: 'URL 或路径',
  absPath: '绝对路径',
  branch: '分支',
  accessKey: '访问密钥',
  credentialsToAccessToTheGitRepositoryItShouldBe: '访问 Git 仓库的凭据。它应该是：',
  ifYouUseGitOrSshUrl: '如果您使用 Git 或 SSH URL。',
  ifYouUseHttpsOrFileUrl: '如果您使用 HTTPS 或文件 URL。',
  none: '无',
  ssh: 'SSH',
  deleteProject: '删除项目',
  save: '保存',
  deleteProject2: '删除项目',
  onceYouDeleteAProjectThereIsNoGoingBackPleaseBeCer: '一旦您删除项目，就无法恢复。请确认。',

  clear_cache: '清除缓存',
  clear_cache_message: '删除与此项目相关的所有缓存文件。此操作不可逆。',

  name2: '名称 *',
  title: '标题 *',
  description: '描述',
  required: '必需',
  key: '{expr}',
  surveyVariables: '调查变量',
  addVariable: '添加变量',
  vaultName: '保管库 ID（可选）',
  vaultNameDefault: '只能存在一个 `default`（空）名称',
  vaultNameUnique: '必须是唯一的',
  vaultTypePassword: '密码',
  vaultTypeScript: '客户端脚本',
  vaultScript: '脚本路径',
  vaultScriptRequired: '脚本路径是必需的',
  vaultScriptClientRequired: '脚本路径必须以“-client”结尾并带有扩展名',
  vaults: '保管库',
  vaultAdd: '添加保管库',
  vaultRequired: '保管库密码是必需的',
  columns: '列',
  buildVersion: '构建版本',
  messageOptional: '消息（可选）',
  debug: '调试',
  dryRun: '干运行',
  diff: '差异',
  advanced: '高级',
  hide: '隐藏',
  pleaseAllowOverridingCliArgumentInTaskTemplateSett: '允许在任务模板设置中覆盖 CLI 参数',
  cliArgsJsonArrayExampleIMyinventoryshPrivatekeythe: 'CLI 参数（JSON 数组）。示例： [ "-i", "@myinventory.sh", "--private-key=/there/id_rsa", "-vvvv" ]',
  started: '已开始',
  author: '作者',
  duration: '持续时间',
  stop: '停止',
  forceStop: '强制停止',
  raw_log: '原始日志',

  confirmTask: '确认',
  deleteTeamMember: '删除团队成员',
  team2: '团队',
  newTeamMember: '新团队成员',
  user: '用户',
  administrator: '管理员',
  definesStartVersionOfYourArtifactEachRunIncrements: '定义您的工件的起始版本。每次运行都会增加工件版本。',
  forMoreInformationAboutBuildingSeeThe: '有关构建的更多信息，请参见',
  taskTemplateReference: '任务模板参考',
  definesWhatArtifactShouldBeDeployedWhenTheTaskRun: '定义在任务运行时应部署的工件。',
  forMoreInformationAboutDeployingSeeThe: '有关部署的更多信息，请参见',
  taskTemplateReference2: '任务模板参考',
  definesAutorunSchedule: '定义自动运行计划。',
  forMoreInformationAboutCronSeeThe: '有关 cron 的更多信息，请参见',
  cronExpressionFormatReference: 'Cron 表达式格式参考',
  startVersion: '起始版本',
  example000: '示例：0.0.0',
  buildTemplate: '构建模板',
  autorun: '自动运行',
  playbookFilename: '剧本文件路径 *',
  exampleSiteyml: '示例：deploy/site.yml',
  inventory2: '库存 *',
  repository: '仓库',
  environment3: '变量组 *',
  vaultPassword: '保管库密码',
  vaultPassword2: '保管库密码',
  view: '视图',
  cron: 'Cron',
  iWantToRunATaskByTheCronOnlyForForNewCommitsOfSome: '我想通过 cron 仅为某个仓库的新提交运行任务',
  repository2: '仓库',
  cronChecksNewCommitBeforeRun: 'Cron 在运行之前检查新提交',
  readThe: '阅读',
  toLearnMoreAboutCron: '以了解有关 Cron 的更多信息。',
  suppressSuccessAlerts: '抑制成功警报',
  cliArgsJsonArrayExampleIMyinventoryshPrivatekeythe2: 'CLI 参数（JSON 数组）。示例： [ "-i", "@myinventory.sh", "--private-key=/there/id_rsa", "-vvvv" ]',
  allowCliArgsInTask: 'CLI 参数',
  docs: '文档',
  editViews: '编辑视图',
  newTemplate: '新模板',
  taskTemplates2: '任务模板',
  all: '所有',
  notLaunched: '未启动',
  by: '由 {user_name}',
  editTemplate: '编辑模板',
  newTemplate2: '新模板',
  deleteTemplate: '删除模板',
  playbook: '剧本',
  email: '电子邮件',
  adminUser: '管理员用户',
  sendAlerts: '发送警报',
  deleteUser: '删除用户',
  newUser: '新用户',
  re: '重新{getActionButtonTitle}',
  teamMember: '{expr} 团队成员',
  taskId: '任务 ID',
  version: '版本',
  status: '状态',
  start: '开始',
  actions: '操作',
  alert: '警报',
  admin: '管理员',
  role: '角色',
  external: '外部',
  time: '时间',
  path: '路径',
  gitUrl: 'Git URL',
  sshKey: 'SSH 密钥',
  lastTask: '最后任务',
  task2: '任务',
  build: '构建',
  deploy: '部署',
  run: '运行',
  add: '添加',
  password_required: '密码是必需的',
  name_required: '名称是必需的',
  user_credentials_required: '用户凭据是必需的',
  type_required: '类型是必需的',
  path_required: '库存文件路径是必需的',
  private_key_required: '私钥是必需的',
  project_name_required: '项目名称是必需的',
  repository_required: '仓库是必需的',
  branch_required: '分支是必需的',
  key_required: '密钥是必需的',
  user_required: '用户是必需的',
  build_version_required: '构建版本是必需的',
  title_required: '标题是必需的',
  isRequired: '是必需的',
  mustBeInteger: '必须是整数',
  mustBe0OrGreater: '必须是 0 或更大',
  start_version_required: '起始版本是必需的',
  playbook_filename_required: '剧本文件名是必需的',
  inventory_required: '库存是必需的',
  environment_required: '环境是必需的',
  email_required: '电子邮件是必需的',
  build_template_required: '构建模板是必需的',
  Task: '任务',
  Build: '构建',
  Deploy: '部署',
  Run: '运行',
  ReBuild: '重建',
  ReDeploy: '重新部署',
  ReRun: '重新运行',
  CreateDemoProject: '创建演示项目',
  LeaveProject: '离开项目',
  integration: '集成',
  integrations: '集成',
  NewIntegration: '新集成',
  EditIntegration: '编辑集成',
  DeleteIntegration: '删除集成',
  DeleteIntegrationMsg: '您确定要删除此集成吗？',
  AddAlias: '添加别名',
  LoadAlias: '加载别名...',
  globalAlias: '使用项目别名',
  matcher: '匹配器',
  matchType: '匹配类型',
  newMatcher: '新匹配器',
  matchMethod: '比较方法',
  matchBodyDataType: '主体数据类型',
  extractValue: '提取值',
  newExtractedValue: '新提取值',
  extractedValueSource: '值来源',
  matchKey: '键',
  matchValue: '值',
  matchOn: '匹配',
  runners: '运行器',
  newRunner: '新运行器',
  enabled: '启用',
  scheduleNextRun: '下次运行',
  maxNumberOfParallelTasks: '最大并行任务数',
  runnerUsage: '用法：',
  runnerDockerCommand: 'Docker：',
  runnerToken: '令牌：',
  editRunner: '编辑运行器',
  deleteRunner: '删除运行器',
  newRunnerToken: '新运行器',
  askDeleteRunner: '您真的想删除运行器 {runner} 吗？',
  project_runners_only_pro: '项目级运行器仅在 <b>专业版</b> 中可用。',
  foss_runners_limited: '开源版本功能有限；完整功能在 <b>专业版</b> 中。',
  learn_more_about_pro: '了解更多',

  projectRestoreResult: '项目恢复结果',
  projectWithNameRestored: '项目 {projectName} 成功恢复。',
  pleaseUpdateAccessKeys: '请在运行任务之前更新它们。',
  emptyKeysRestored: '{emptyKeys} 个空密钥已添加。',
  template: '模板',
  aliasUrlCopied: '别名 URL 已复制到剪贴板。',
  yes: '是',
  activeTasks: '活动任务',
  taskLocation: '位置',
  empty: '空',
  noValues: '没有值',
  addArg: '添加参数',

  status_success: '成功',
  status_failed: '失败',
  status_stopped: '已停止',

  api_tokens: 'API 令牌',

  // Terraform/OpenTofu
  auto_approve: '自动批准',

  // Ansible
  tag: '标签',
  tag_required: '标签是必需的',
  allowInventoryInTask: '库存',
  allowLimitInTask: '限制',
  addLimit: '添加限制',
  allowDebug: '调试',
  addTag: '添加标签',
  skipTags: '跳过标签',
  addSkippedTag: '添加跳过的标签',
  tags: '标签',
  limit: '限制',

  runner_tag: '运行器标签',
  task_prompts: '提示',
  template_advanced: '高级选项',
  template_app_options: '{app} 选项',
  template_app_prompts: '{app} 提示',
  general_settings: '常规',
  danger_zone_settings: '危险区域',
  project_stats: '统计',
  allow_override_branch: '分支',
  template_common_options: '常见选项',
};
