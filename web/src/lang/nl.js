export default {
  checkInterval: 'controleerInterval',
  newCommitCheckInterval: 'Nieuwe commit controle interval',
  schedule: 'Schema',
  newSchedule: 'Nieuw Schema',
  deleteSchedule: 'Schema Verwijderen',
  editSchedule: 'Schema Bewerken',
  backup: 'Project Back-up',
  downloadTheProjectBackupFile: 'Download het project back-up bestand.',
  restoreProject: 'Project Herstellen...',
  incorrectUsrPwd: 'Onjuiste inlog of wachtwoord',
  askDeleteUser: 'Wilt u deze gebruiker echt verwijderen?',
  askDeleteTemp: 'Wilt u deze sjabloon echt verwijderen?',
  askDeleteEnv: 'Wilt u deze omgeving echt verwijderen?',
  askDeleteInv: 'Wilt u deze inventaris echt verwijderen?',
  ask<PERSON><PERSON><PERSON><PERSON>ey: 'Wilt u deze sleutel echt verwijderen?',
  askDeleteRepo: 'Wilt u dit repository echt verwijderen?',
  askDeleteProj: 'Wilt u dit project echt verwijderen?',
  askD<PERSON><PERSON>TMem: 'Wilt u dit teamlid echt verwijderen?',
  askDeleteSchedule: 'Wilt u dit schema echt verwijderen?',
  edit: 'Bewerken',
  nnew: 'Nieuw',
  keyFormSshKey: 'SSH Sleutel',
  keyFormLoginPassword: 'Inloggen met wachtwoord',
  keyFormNone: 'Geen',
  incorrectUrl: 'Onjuiste URL',
  username: 'Gebruikersnaam',
  username_required: 'Gebruikersnaam is vereist',
  dashboard: 'Dashboard',
  history: 'Geschiedenis',
  activity: 'Activiteit',
  settings: 'Instellingen',
  signIn: 'Inloggen',
  password: 'Wachtwoord',
  changePassword: 'Wachtwoord Wijzigen',
  editUser: 'Gebruiker Bewerken',
  newProject: 'Nieuw Project',
  close: 'Sluiten',
  newProject2: 'Nieuw Project...',
  demoMode: 'DEMO MODUS',
  task: 'Taak #{expr}',
  youCanRunAnyTasks: 'U kunt elke taak uitvoeren',
  youHaveReadonlyAccess: 'U heeft alleen-lezen toegang',
  taskTemplates: 'Taak Sjablonen',
  inventory: 'Inventaris',
  environment: 'Variabele Groepen',
  keyStore: 'Sleutel Opslag',
  repositories: 'Repositories',
  darkMode: 'Donkere Modus',
  team: 'Team',
  users: 'Gebruikers',
  editAccount: 'Account Bewerken',
  signOut: 'Uitloggen',
  error: 'Fout',
  refreshPage: 'Vernieuw Pagina',
  relogin: 'Opnieuw Inloggen',
  howToFixSigninIssues: 'Hoe inlogproblemen op te lossen',
  firstlyYouNeedAccessToTheServerWhereSemaphoreRunni: 'Ten eerste heeft u toegang nodig tot de server waar Semaphore draait.',
  executeTheFollowingCommandOnTheServerToSeeExisting: 'Voer de volgende opdracht op de server uit om bestaande gebruikers te zien:',
  semaphoreUserList: 'semaphore gebruikerslijst',
  youCanChangePasswordOfExistingUser: 'U kunt het wachtwoord van een bestaande gebruiker wijzigen:',
  semaphoreUserChangebyloginLoginUser123Password: 'semaphore gebruiker wijzig-per-inlog --login gebruiker123 --wachtwoord {maakWachtwoordVoorbeeld}',
  orCreateNewAdminUser: 'Of maak een nieuwe admin gebruiker:',
  close2: 'Sluiten',
  semaphore: 'SEMAPHORE',
  dontHaveAccountOrCantSignIn: 'Heeft u geen account of kunt u niet inloggen?',
  password2: 'Wachtwoord',
  cancel: 'Annuleren',
  noViews: 'Geen weergaven',
  addView: 'Weergave Toevoegen',
  editEnvironment: 'Variabele Groep Bewerken',
  deleteEnvironment: 'Variabele Groep Verwijderen',
  newEnvironment: 'Nieuwe Groep',
  environmentName: 'Groepsnaam',
  extraVariables: 'Extra variabelen',
  enterExtraVariablesJson: 'Voer extra variabelen JSON in...',
  environmentVariables: 'Omgevingsvariabelen',
  enterEnvJson: 'Voer env JSON in...',
  environmentAndExtraVariablesMustBeValidJsonExample: 'Omgevings- en extra variabelen moeten geldige JSON zijn. Voorbeeld:',
  dashboard2: 'Dashboard',
  ansibleSemaphore: 'Semaphore UI',
  wereSorryButHtmlwebpackpluginoptionstitleDoesntWor: 'Het spijt ons, maar <%= htmlWebpackPlugin.options.title %> werkt niet goed zonder ingeschakelde JavaScript. Schakel het in om door te gaan.',
  deleteInventory: 'Verwijder inventaris',
  newInventory: 'Nieuwe Inventaris',
  name: 'Naam',
  userCredentials: 'Gebruikersreferenties',
  sudoCredentialsOptional: 'Sudo Referenties (Optioneel)',
  type: 'Type',
  pathToInventoryFile: 'Pad naar Inventarisbestand',
  enterInventory: 'Voer inventaris in...',
  staticInventoryExample: 'Statische inventarisvoorbeeld:',
  staticYamlInventoryExample: 'Statisch YAML inventarisvoorbeeld:',
  keyName: 'Sleutelnaam',
  loginOptional: 'Inloggen (Optioneel)',
  usernameOptional: 'Gebruikersnaam (Optioneel)',
  privateKey: 'Privésleutel',
  override: 'Overschrijven',
  useThisTypeOfKeyForHttpsRepositoriesAndForPlaybook: 'Gebruik dit type sleutel voor HTTPS repositories en voor playbooks die geen SSH-verbindingen gebruiken.',
  deleteKey: 'Sleutel Verwijderen',
  newKey: 'Nieuwe Sleutel',
  create: 'Aanmaken',
  newTask: 'Nieuwe Taak',
  cantDeleteThe: 'Kan de {objectTitle} niet verwijderen',
  theCantBeDeletedBecauseItUsedByTheResourcesBelow: 'De {objectTitle} kan niet worden verwijderd omdat deze wordt gebruikt door de onderstaande bronnen',
  projectName: 'Projectnaam',
  allowAlertsForThisProject: 'Sta waarschuwingen voor dit project toe',
  telegramChatIdOptional: 'Telegram Chat ID (Optioneel)',
  maxNumberOfParallelTasksOptional: 'Maximaal aantal parallelle taken (Optioneel)',
  deleteRepository: 'Repository Verwijderen',
  newRepository: 'Nieuwe Repository',
  urlOrPath: 'URL of pad',
  absPath: 'absoluut pad',
  branch: 'Tak',
  accessKey: 'Toegangssleutel',
  credentialsToAccessToTheGitRepositoryItShouldBe: 'Referenties om toegang te krijgen tot het Git-repository. Het moet zijn:',
  ifYouUseGitOrSshUrl: 'als u Git of SSH URL gebruikt.',
  ifYouUseHttpsOrFileUrl: 'als u HTTPS of bestand URL gebruikt.',
  none: 'Geen',
  ssh: 'SSH',
  deleteProject: 'Project Verwijderen',
  save: 'Opslaan',
  deleteProject2: 'Project Verwijderen',
  onceYouDeleteAProjectThereIsNoGoingBackPleaseBeCer: 'Zodra u een project verwijdert, is er geen weg terug. Wees alstublieft zeker.',

  clear_cache: 'Cache wissen',
  clear_cache_message: 'Verwijder alle cachebestanden die aan dit project zijn gerelateerd. Deze actie is onomkeerbaar.',

  name2: 'Naam *',
  title: 'Titel *',
  description: 'Beschrijving',
  required: 'Vereist',
  key: '{expr}',
  surveyVariables: 'Enquête Variabelen',
  addVariable: 'Variabele Toevoegen',
  vaultName: 'Kluis ID (optioneel)',
  vaultNameDefault: 'Er mag slechts één `default` (leeg) naam bestaan',
  vaultNameUnique: 'Moet uniek zijn',
  vaultTypePassword: 'Wachtwoord',
  vaultTypeScript: 'Client Script',
  vaultScript: 'Script Pad',
  vaultScriptRequired: 'Script Pad is vereist',
  vaultScriptClientRequired: 'Script pad moet eindigen met \'-client\' en extensie',
  vaults: 'Kluis',
  vaultAdd: 'Voeg Kluis Toe',
  vaultRequired: 'Kluis Wachtwoord is vereist',
  columns: 'Kolommen',
  buildVersion: 'Bouw Versie',
  messageOptional: 'Bericht (Optioneel)',
  debug: 'Debug',
  dryRun: 'Droge Run',
  diff: 'Verschil',
  advanced: 'Geavanceerd',
  hide: 'Verbergen',
  pleaseAllowOverridingCliArgumentInTaskTemplateSett: 'Om het overschrijven van CLI-argumenten in Taak Sjablooninstellingen toe te staan',
  cliArgsJsonArrayExampleIMyinventoryshPrivatekeythe: 'CLI Args (JSON-array). Voorbeeld: [ "-i", "@myinventory.sh", "--private-key=/there/id_rsa", "-vvvv" ]',
  started: 'Gestart',
  author: 'Auteur',
  duration: 'Duur',
  stop: 'Stop',
  forceStop: 'Force Stop',
  raw_log: 'Ruwe log',

  confirmTask: 'Bevestigen',
  deleteTeamMember: 'Teamlid Verwijderen',
  team2: 'Team',
  newTeamMember: 'Nieuw Teamlid',
  user: 'Gebruiker',
  administrator: 'Administrator',
  definesStartVersionOfYourArtifactEachRunIncrements: 'Definieert de startversie van uw artifact. Elke run verhoogt de artifactversie.',
  forMoreInformationAboutBuildingSeeThe: 'Voor meer informatie over bouwen, zie de',
  taskTemplateReference: 'Taak Sjabloon referentie',
  definesWhatArtifactShouldBeDeployedWhenTheTaskRun: 'Definieert welk artifact moet worden gedeployed wanneer de taak draait.',
  forMoreInformationAboutDeployingSeeThe: 'Voor meer informatie over deployen, zie de',
  taskTemplateReference2: 'Taak Sjabloon referentie',
  definesAutorunSchedule: 'Definieert autorun schema.',
  forMoreInformationAboutCronSeeThe: 'Voor meer informatie over cron, zie de',
  cronExpressionFormatReference: 'Cron expressie formaat referentie',
  startVersion: 'Start Versie',
  example000: 'Voorbeeld: 0.0.0',
  buildTemplate: 'Bouw Sjabloon',
  autorun: 'Autorun',
  playbookFilename: 'Pad naar playbook bestand *',
  exampleSiteyml: 'Voorbeeld: deploy/site.yml',
  inventory2: 'Inventaris *',
  repository: 'Repository',
  environment3: 'Variabele Groep *',
  vaultPassword: 'Kluis Wachtwoord',
  vaultPassword2: 'Kluis Wachtwoord',
  view: 'Weergave',
  cron: 'Cron',
  iWantToRunATaskByTheCronOnlyForForNewCommitsOfSome: 'Ik wil een taak via cron alleen uitvoeren voor nieuwe commits van een bepaalde repository',
  repository2: 'Repository',
  cronChecksNewCommitBeforeRun: 'Cron controleert nieuwe commit voor run',
  readThe: 'Lees de',
  toLearnMoreAboutCron: 'om meer te leren over Cron.',
  suppressSuccessAlerts: 'Suppress success alerts',
  cliArgsJsonArrayExampleIMyinventoryshPrivatekeythe2: 'CLI Args (JSON-array). Voorbeeld: [ "-i", "@myinventory.sh", "--private-key=/there/id_rsa", "-vvvv" ]',
  allowCliArgsInTask: 'CLI-argumenten',
  docs: 'documentatie',
  editViews: 'Weergaven Bewerken',
  newTemplate: 'Nieuw sjabloon',
  taskTemplates2: 'Taak Sjablonen',
  all: 'Alle',
  notLaunched: 'Niet gelanceerd',
  by: 'door {user_name}',
  editTemplate: 'Sjabloon Bewerken',
  newTemplate2: 'Nieuw Sjabloon',
  deleteTemplate: 'Sjabloon Verwijderen',
  playbook: 'Playbook',
  email: 'E-mail',
  adminUser: 'Admin gebruiker',
  sendAlerts: 'Stuur waarschuwingen',
  deleteUser: 'Verwijder gebruiker',
  newUser: 'Nieuwe Gebruiker',
  re: 'Re{getActionButtonTitle}',
  teamMember: '{expr} Teamlid',
  taskId: 'Taak ID',
  version: 'Versie',
  status: 'Status',
  start: 'Start',
  actions: 'Acties',
  alert: 'Waarschuwing',
  admin: 'Admin',
  role: 'Rol',
  external: 'Extern',
  time: 'Tijd',
  path: 'Pad',
  gitUrl: 'Git URL',
  sshKey: 'SSH Sleutel',
  lastTask: 'Laatste Taak',
  task2: 'Taak',
  build: 'Bouwen',
  deploy: 'Deployen',
  run: 'Uitvoeren',
  add: 'Toevoegen',
  password_required: 'Wachtwoord is vereist',
  name_required: 'Naam is vereist',
  user_credentials_required: 'Gebruikersreferenties zijn vereist',
  type_required: 'Type is vereist',
  path_required: 'Pad naar Inventarisbestand is vereist',
  private_key_required: 'Privésleutel is vereist',
  project_name_required: 'Projectnaam is vereist',
  repository_required: 'Repository is vereist',
  branch_required: 'Tak is vereist',
  key_required: 'Sleutel is vereist',
  user_required: 'Gebruiker is vereist',
  build_version_required: 'Bouwversie is vereist',
  title_required: 'Titel is vereist',
  isRequired: 'is vereist',
  mustBeInteger: 'Moet een geheel getal zijn',
  mustBe0OrGreater: 'Moet 0 of groter zijn',
  start_version_required: 'Startversie is vereist',
  playbook_filename_required: 'Playbook-bestandsnaam is vereist',
  inventory_required: 'Inventaris is vereist',
  environment_required: 'Omgeving is vereist',
  email_required: 'E-mail is vereist',
  build_template_required: 'Bouwsjabloon is vereist',
  Task: 'Taak',
  Build: 'Bouwen',
  Deploy: 'Deployen',
  Run: 'Uitvoeren',
  ReBuild: 'Herbouwen',
  ReDeploy: 'Herdeployen',
  ReRun: 'Heruitvoeren',
  CreateDemoProject: 'Maak Demo Project',
  LeaveProject: 'Verlaat Project',
  integration: 'Integratie',
  integrations: 'Integraties',
  NewIntegration: 'Nieuwe Integratie',
  EditIntegration: 'Integratie Bewerken',
  DeleteIntegration: 'Integratie Verwijderen',
  DeleteIntegrationMsg: 'Weet u zeker dat u deze integratie wilt verwijderen?',
  AddAlias: 'Voeg Alias Toe',
  LoadAlias: 'Aliassen laden...',
  globalAlias: 'Gebruik projectalias',
  matcher: 'Matcher',
  matchType: 'Vergelijkings Type',
  newMatcher: 'Nieuwe Matcher',
  matchMethod: 'Vergelijkings Methode',
  matchBodyDataType: 'Body Gegevens Type',
  extractValue: 'Waarde Extraheren',
  newExtractedValue: 'Nieuwe Geëxtraheerde Waarde',
  extractedValueSource: 'Waarde Bron',
  matchKey: 'Sleutel',
  matchValue: 'Waarde',
  matchOn: 'Vergelijk op',
  runners: 'Runners',
  newRunner: 'Nieuwe Runner',
  enabled: 'Ingeschakeld',
  scheduleNextRun: 'Volgende run',
  maxNumberOfParallelTasks: 'Maximaal aantal parallelle taken',
  runnerUsage: 'Gebruik:',
  runnerDockerCommand: 'Docker:',
  runnerToken: 'Token:',
  editRunner: 'Runner Bewerken',
  deleteRunner: 'Runner Verwijderen',
  newRunnerToken: 'Nieuwe Runner',
  askDeleteRunner: 'Wilt u runner {runner} echt verwijderen?',
  project_runners_only_pro: 'Projectniveau runners zijn alleen beschikbaar in de <b>PRO</b> versie.',
  foss_runners_limited: 'De open-source versie heeft beperkte functionaliteit; volledige functionaliteit is in de <b>PRO</b> versie.',
  learn_more_about_pro: 'Leer meer',

  projectRestoreResult: 'Project herstelresultaten',
  projectWithNameRestored: 'Project {projectName} succesvol hersteld.',
  pleaseUpdateAccessKeys: 'Werk ze alstublieft bij voordat u taken uitvoert.',
  emptyKeysRestored: '{emptyKeys} lege sleutels toegevoegd.',
  template: 'Sjabloon',
  aliasUrlCopied: 'De alias-URL is naar het klembord gekopieerd.',
  yes: 'Ja',
  activeTasks: 'Actieve Taken',
  taskLocation: 'Locatie',
  empty: 'Leeg',
  noValues: 'Geen waarden',
  addArg: 'Voeg arg toe',

  status_success: 'Succes',
  status_failed: 'Mislukt',
  status_stopped: 'Gestopt',

  api_tokens: 'API Tokens',

  // Terraform/OpenTofu
  auto_approve: 'Automatisch goedkeuren',

  // Ansible
  tag: 'Tag',
  tag_required: 'Tag is vereist',
  allowInventoryInTask: 'Inventaris',
  allowLimitInTask: 'Limiet',
  addLimit: 'Voeg limiet toe',
  allowDebug: 'Debug',
  addTag: 'Voeg tag toe',
  skipTags: 'Sla tags over',
  addSkippedTag: 'Voeg overgeslagen tag toe',
  tags: 'Tags',
  limit: 'Limiet',

  runner_tag: 'Runner tag',
  task_prompts: 'Vragen',
  template_advanced: 'Geavanceerde opties',
  template_app_options: '{app} opties',
  template_app_prompts: '{app} vragen',
  general_settings: 'Algemeen',
  danger_zone_settings: 'Gevarenzone',
  project_stats: 'Statistieken',
  allow_override_branch: 'Tak',
  template_common_options: 'Gemeenschappelijke opties',
};
