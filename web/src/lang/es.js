export default {
  checkInterval: 'intervaloDeVerificación',
  newCommitCheckInterval: 'Nuevo intervalo de verificación de commits',
  schedule: 'Horario',
  newSchedule: 'Nuevo Horario',
  deleteSchedule: 'Eliminar Horario',
  editSchedule: 'Editar Horario',
  backup: '<PERSON><PERSON><PERSON> del Proyecto',
  downloadTheProjectBackupFile: 'Descargar el archivo de respaldo del proyecto.',
  restoreProject: 'Restaurar Proyecto...',
  incorrectUsrPwd: 'Usuario o contraseña incorrectos',
  askDeleteUser: '¿Realmente quieres eliminar a este usuario?',
  askDeleteTemp: '¿Realmente quieres eliminar esta plantilla?',
  askDeleteEnv: '¿Realmente quieres eliminar este entorno?',
  askDeleteInv: '¿Realmente quieres eliminar este inventario?',
  askDele<PERSON><PERSON>ey: '¿Realmente quieres eliminar esta clave?',
  askDeleteRepo: '¿Realmente quieres eliminar este repositorio?',
  ask<PERSON><PERSON>teProj: '¿Realmente quieres eliminar este proyecto?',
  askDeleteTMem: '¿Realmente quieres eliminar a este miembro del equipo?',
  askDeleteSchedule: '¿Realmente quieres eliminar este horario?',
  edit: 'Editar',
  nnew: 'Nuevo',
  keyFormSshKey: 'Clave SSH',
  keyFormLoginPassword: 'Iniciar sesión con contraseña',
  keyFormNone: 'Ninguno',
  incorrectUrl: 'URL incorrecta',
  username: 'Nombre de usuario',
  username_required: 'Se requiere nombre de usuario',
  dashboard: 'Tablero',
  history: 'Historia',
  activity: 'Actividad',
  settings: 'Configuraciones',
  signIn: 'Iniciar Sesión',
  password: 'Contraseña',
  changePassword: 'Cambiar contraseña',
  editUser: 'Editar Usuario',
  newProject: 'Nuevo Proyecto',
  close: 'Cerrar',
  newProject2: 'Nuevo Proyecto...',
  demoMode: 'MODO DEMO',
  task: 'Tarea #{expr}',
  youCanRunAnyTasks: 'Puedes ejecutar cualquier tarea',
  youHaveReadonlyAccess: 'Tienes acceso de solo lectura',
  taskTemplates: 'Plantillas de Tareas',
  inventory: 'Inventario',
  environment: 'Grupos de Variables',
  keyStore: 'Almacén de Claves',
  repositories: 'Repositorios',
  darkMode: 'Modo Oscuro',
  team: 'Equipo',
  users: 'Usuarios',
  editAccount: 'Editar Cuenta',
  signOut: 'Cerrar Sesión',
  error: 'Error',
  refreshPage: 'Actualizar Página',
  relogin: 'Reiniciar sesión',
  howToFixSigninIssues: 'Cómo solucionar problemas de inicio de sesión',
  firstlyYouNeedAccessToTheServerWhereSemaphoreRunni: 'Primero, necesitas acceso al servidor donde se ejecuta Semaphore.',
  executeTheFollowingCommandOnTheServerToSeeExisting: 'Ejecuta el siguiente comando en el servidor para ver los usuarios existentes:',
  semaphoreUserList: 'lista de usuarios de semaphore',
  youCanChangePasswordOfExistingUser: 'Puedes cambiar la contraseña de un usuario existente:',
  semaphoreUserChangebyloginLoginUser123Password: 'cambio de usuario de semaphore --login user123 --password {makePasswordExample}',
  orCreateNewAdminUser: 'O crea un nuevo usuario administrador:',
  close2: 'Cerrar',
  semaphore: 'SEMÁFORO',
  dontHaveAccountOrCantSignIn: '¿No tienes cuenta o no puedes iniciar sesión?',
  password2: 'Contraseña',
  cancel: 'Cancelar',
  noViews: 'Sin vistas',
  addView: 'Agregar vista',
  editEnvironment: 'Editar Grupo de Variables',
  deleteEnvironment: 'Eliminar grupo de variables',
  newEnvironment: 'Nuevo Grupo',
  environmentName: 'Nombre del Grupo',
  extraVariables: 'Variables adicionales',
  enterExtraVariablesJson: 'Ingresa variables adicionales en JSON...',
  environmentVariables: 'Variables de entorno',
  enterEnvJson: 'Ingresa JSON de entorno...',
  environmentAndExtraVariablesMustBeValidJsonExample: 'El entorno y las variables adicionales deben ser JSON válidos. Ejemplo:',
  dashboard2: 'Tablero',
  ansibleSemaphore: 'Interfaz de Semaphore',
  wereSorryButHtmlwebpackpluginoptionstitleDoesntWor: 'Lo sentimos, pero <%= htmlWebpackPlugin.options.title %> no funciona correctamente sin JavaScript habilitado. Por favor, habilítalo para continuar.',
  deleteInventory: 'Eliminar inventario',
  newInventory: 'Nuevo Inventario',
  name: 'Nombre',
  userCredentials: 'Credenciales de Usuario',
  sudoCredentialsOptional: 'Credenciales de Sudo (Opcional)',
  type: 'Tipo',
  pathToInventoryFile: 'Ruta al archivo de Inventario',
  enterInventory: 'Ingresa inventario...',
  staticInventoryExample: 'Ejemplo de inventario estático:',
  staticYamlInventoryExample: 'Ejemplo de inventario YAML estático:',
  keyName: 'Nombre de la Clave',
  loginOptional: 'Inicio de sesión (Opcional)',
  usernameOptional: 'Nombre de usuario (Opcional)',
  privateKey: 'Clave Privada',
  override: 'Sobrescribir',
  useThisTypeOfKeyForHttpsRepositoriesAndForPlaybook: 'Usa este tipo de clave para repositorios HTTPS y para playbooks que utilizan conexiones no SSH.',
  deleteKey: 'Eliminar clave',
  newKey: 'Nueva Clave',
  create: 'Crear',
  newTask: 'Nueva Tarea',
  cantDeleteThe: 'No se puede eliminar el {objectTitle}',
  theCantBeDeletedBecauseItUsedByTheResourcesBelow: 'El {objectTitle} no se puede eliminar porque está siendo utilizado por los recursos a continuación',
  projectName: 'Nombre del Proyecto',
  allowAlertsForThisProject: 'Permitir alertas para este proyecto',
  telegramChatIdOptional: 'ID de Chat de Telegram (Opcional)',
  maxNumberOfParallelTasksOptional: 'Número máximo de tareas paralelas (Opcional)',
  deleteRepository: 'Eliminar repositorio',
  newRepository: 'Nuevo Repositorio',
  urlOrPath: 'URL o ruta',
  absPath: 'ruta abs.',
  branch: 'Rama',
  accessKey: 'Clave de Acceso',
  credentialsToAccessToTheGitRepositoryItShouldBe: 'Credenciales para acceder al repositorio de Git. Debe ser:',
  ifYouUseGitOrSshUrl: 'si usas URL de Git o SSH.',
  ifYouUseHttpsOrFileUrl: 'si usas URL de HTTPS o archivo.',
  none: 'Ninguno',
  ssh: 'SSH',
  deleteProject: 'Eliminar proyecto',
  save: 'Guardar',
  deleteProject2: 'Eliminar Proyecto',
  onceYouDeleteAProjectThereIsNoGoingBackPleaseBeCer: 'Una vez que eliminas un proyecto, no hay vuelta atrás. Por favor, asegúrate.',

  clear_cache: 'Limpiar caché',
  clear_cache_message: 'Eliminar todos los archivos de caché relacionados con este proyecto. Esta acción es irreversible.',

  name2: 'Nombre *',
  title: 'Título *',
  description: 'Descripción',
  required: 'Requerido',
  key: '{expr}',
  surveyVariables: 'Variables de Encuesta',
  addVariable: 'Agregar variable',
  vaultName: 'ID de Bóveda (opcional)',
  vaultNameDefault: 'Solo puede existir un nombre `default` (vacío)',
  vaultNameUnique: 'Debe ser único',
  vaultTypePassword: 'Contraseña',
  vaultTypeScript: 'Script del Cliente',
  vaultScript: 'Ruta del Script',
  vaultScriptRequired: 'La ruta del script es requerida',
  vaultScriptClientRequired: 'La ruta del script debe terminar con \'-client\' y extensión',
  vaults: 'Bóvedas',
  vaultAdd: 'Agregar Bóveda',
  vaultRequired: 'Se requiere la Contraseña de la Bóveda',
  columns: 'Columnas',
  buildVersion: 'Versión de Construcción',
  messageOptional: 'Mensaje (Opcional)',
  debug: 'Depurar',
  dryRun: 'Ejecución en Seco',
  diff: 'Diferencia',
  advanced: 'Avanzado',
  hide: 'Ocultar',
  pleaseAllowOverridingCliArgumentInTaskTemplateSett: 'Para permitir sobrescribir el argumento CLI en la configuración de la Plantilla de Tarea',
  cliArgsJsonArrayExampleIMyinventoryshPrivatekeythe: 'Argumentos CLI (array JSON). Ejemplo: [ "-i", "@myinventory.sh", "--private-key=/there/id_rsa", "-vvvv" ]',
  started: 'Iniciado',
  author: 'Autor',
  duration: 'Duración',
  stop: 'Detener',
  forceStop: 'Detener Forzosamente',
  raw_log: 'Registro en bruto',

  confirmTask: 'Confirmar',
  deleteTeamMember: 'Eliminar miembro del equipo',
  team2: 'Equipo',
  newTeamMember: 'Nuevo Miembro del Equipo',
  user: 'Usuario',
  administrator: 'Administrador',
  definesStartVersionOfYourArtifactEachRunIncrements: 'Define la versión inicial de tu artefacto. Cada ejecución incrementa la versión del artefacto.',
  forMoreInformationAboutBuildingSeeThe: 'Para más información sobre la construcción, consulta el',
  taskTemplateReference: 'Referencia de Plantilla de Tarea',
  definesWhatArtifactShouldBeDeployedWhenTheTaskRun: 'Define qué artefacto debe ser desplegado cuando se ejecute la tarea.',
  forMoreInformationAboutDeployingSeeThe: 'Para más información sobre el despliegue, consulta el',
  taskTemplateReference2: 'Referencia de Plantilla de Tarea',
  definesAutorunSchedule: 'Define el horario de autorun.',
  forMoreInformationAboutCronSeeThe: 'Para más información sobre cron, consulta el',
  cronExpressionFormatReference: 'Referencia de formato de expresión cron',
  startVersion: 'Versión Inicial',
  example000: 'Ejemplo: 0.0.0',
  buildTemplate: 'Plantilla de Construcción',
  autorun: 'Autorun',
  playbookFilename: 'Ruta al archivo de playbook *',
  exampleSiteyml: 'Ejemplo: deploy/site.yml',
  inventory2: 'Inventario *',
  repository: 'Repositorio',
  environment3: 'Grupo de Variables *',
  vaultPassword: 'Contraseña de la Bóveda',
  vaultPassword2: 'Contraseña de la Bóveda',
  view: 'Vista',
  cron: 'Cron',
  iWantToRunATaskByTheCronOnlyForForNewCommitsOfSome: 'Quiero ejecutar una tarea por cron solo para nuevos commits de algún repositorio',
  repository2: 'Repositorio',
  cronChecksNewCommitBeforeRun: 'Cron verifica nuevos commits antes de ejecutar',
  readThe: 'Lee el',
  toLearnMoreAboutCron: 'para aprender más sobre Cron.',
  suppressSuccessAlerts: 'Suprimir alertas de éxito',
  cliArgsJsonArrayExampleIMyinventoryshPrivatekeythe2: 'Argumentos CLI (array JSON). Ejemplo: [ "-i", "@myinventory.sh", "--private-key=/there/id_rsa", "-vvvv" ]',
  allowCliArgsInTask: 'Argumentos CLI',
  docs: 'documentos',
  editViews: 'Editar Vistas',
  newTemplate: 'Nueva plantilla',
  taskTemplates2: 'Plantillas de Tareas',
  all: 'Todo',
  notLaunched: 'No lanzado',
  by: 'por {user_name}',
  editTemplate: 'Editar Plantilla',
  newTemplate2: 'Nueva Plantilla',
  deleteTemplate: 'Eliminar plantilla',
  playbook: 'Playbook',
  email: 'Correo Electrónico',
  adminUser: 'Usuario Administrador',
  sendAlerts: 'Enviar alertas',
  deleteUser: 'Eliminar usuario',
  newUser: 'Nuevo Usuario',
  re: 'Re{getActionButtonTitle}',
  teamMember: '{expr} Miembro del Equipo',
  taskId: 'ID de Tarea',
  version: 'Versión',
  status: 'Estado',
  start: 'Iniciar',
  actions: 'Acciones',
  alert: 'Alerta',
  admin: 'Admin',
  role: 'Rol',
  external: 'Externo',
  time: 'Tiempo',
  path: 'Ruta',
  gitUrl: 'URL de Git',
  sshKey: 'Clave SSH',
  lastTask: 'Última Tarea',
  task2: 'Tarea',
  build: 'Construir',
  deploy: 'Desplegar',
  run: 'Ejecutar',
  add: 'Agregar',
  password_required: 'Se requiere contraseña',
  name_required: 'Se requiere nombre',
  user_credentials_required: 'Se requieren credenciales de usuario',
  type_required: 'Se requiere tipo',
  path_required: 'Se requiere ruta al archivo de Inventario',
  private_key_required: 'Se requiere clave privada',
  project_name_required: 'Se requiere nombre del proyecto',
  repository_required: 'Se requiere repositorio',
  branch_required: 'Se requiere rama',
  key_required: 'Se requiere clave',
  user_required: 'Se requiere usuario',
  build_version_required: 'Se requiere versión de construcción',
  title_required: 'Se requiere título',
  isRequired: 'es requerido',
  mustBeInteger: 'Debe ser un número entero',
  mustBe0OrGreater: 'Debe ser 0 o mayor',
  start_version_required: 'Se requiere versión inicial',
  playbook_filename_required: 'Se requiere nombre de archivo de playbook',
  inventory_required: 'Se requiere inventario',
  environment_required: 'Se requiere entorno',
  email_required: 'Se requiere correo electrónico',
  build_template_required: 'Se requiere plantilla de construcción',
  Task: 'Tarea',
  Build: 'Construir',
  Deploy: 'Desplegar',
  Run: 'Ejecutar',
  ReBuild: 'Reconstruir',
  ReDeploy: 'Re-desplegar',
  ReRun: 'Re-ejecutar',
  CreateDemoProject: 'Crear Proyecto Demo',
  LeaveProject: 'Salir del Proyecto',
  integration: 'Integración',
  integrations: 'Integraciones',
  NewIntegration: 'Nueva Integración',
  EditIntegration: 'Editar Integración',
  DeleteIntegration: 'Eliminar Integración',
  DeleteIntegrationMsg: '¿Estás seguro de que deseas eliminar esta Integración?',
  AddAlias: 'Agregar Alias',
  LoadAlias: 'Cargando alias...',
  globalAlias: 'Usar alias del proyecto',
  matcher: 'Comparador',
  matchType: 'Tipo de Comparación',
  newMatcher: 'Nuevo Comparador',
  matchMethod: 'Método de Comparación',
  matchBodyDataType: 'Tipo de Datos del Cuerpo',
  extractValue: 'Extraer Valor',
  newExtractedValue: 'Nuevo Valor Extraído',
  extractedValueSource: 'Fuente del Valor',
  matchKey: 'Clave',
  matchValue: 'Valor',
  matchOn: 'Comparar en',
  runners: 'Ejecutores',
  newRunner: 'Nuevo Ejecutor',
  enabled: 'Habilitado',
  scheduleNextRun: 'Próxima ejecución',
  maxNumberOfParallelTasks: 'Número máximo de tareas paralelas',
  runnerUsage: 'Uso:',
  runnerDockerCommand: 'Docker:',
  runnerToken: 'Token:',
  editRunner: 'Editar Ejecutor',
  deleteRunner: 'Eliminar Ejecutor',
  newRunnerToken: 'Nuevo Ejecutor',
  askDeleteRunner: '¿Realmente quieres eliminar el ejecutor {runner}?',
  project_runners_only_pro: 'Los ejecutores a nivel de proyecto solo están disponibles en la versión <b>PRO</b>.',
  foss_runners_limited: 'La versión de código abierto tiene funcionalidad limitada; la funcionalidad completa está en la versión <b>PRO</b>.',
  learn_more_about_pro: 'Aprende más',

  projectRestoreResult: 'Resultados de la restauración del proyecto',
  projectWithNameRestored: 'Proyecto {projectName} restaurado con éxito.',
  pleaseUpdateAccessKeys: 'Por favor, actualiza las claves de acceso antes de ejecutar tareas.',
  emptyKeysRestored: '{emptyKeys} claves vacías añadidas.',
  template: 'Plantilla',
  aliasUrlCopied: 'La URL del alias ha sido copiada al portapapeles.',
  yes: 'Sí',
  activeTasks: 'Tareas Activas',
  taskLocation: 'Ubicación',
  empty: 'Vacío',
  noValues: 'Sin valores',
  addArg: 'Agregar argumento',

  status_success: 'Éxito',
  status_failed: 'Fallido',
  status_stopped: 'Detenido',

  api_tokens: 'Tokens de API',

  // Terraform/OpenTofu
  auto_approve: 'Aprobación automática',

  // Ansible
  tag: 'Etiqueta',
  tag_required: 'Se requiere etiqueta',
  allowInventoryInTask: 'Inventario',
  allowLimitInTask: 'Límite',
  addLimit: 'Agregar límite',
  allowDebug: 'Depurar',
  addTag: 'Agregar etiqueta',
  skipTags: 'Omitir etiquetas',
  addSkippedTag: 'Agregar etiqueta omitida',
  tags: 'Etiquetas',
  limit: 'Límite',

  runner_tag: 'Etiqueta del ejecutor',
  task_prompts: 'Solicitudes',
  template_advanced: 'Opciones avanzadas',
  template_app_options: 'Opciones de {app}',
  template_app_prompts: 'Solicitudes de {app}',
  general_settings: 'General',
  danger_zone_settings: 'Zona de Peligro',
  project_stats: 'Estadísticas',
  allow_override_branch: 'Rama',
  template_common_options: 'Opciones comunes',
};
