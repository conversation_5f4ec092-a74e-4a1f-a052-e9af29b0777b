export default {
  checkInterval: 'intervaloDeVerificação',
  newCommitCheckInterval: 'Intervalo de verificação de novos commits',
  schedule: 'Agenda',
  newSchedule: 'Nova Agenda',
  deleteSchedule: 'Excluir Agenda',
  editSchedule: 'Editar Agenda',
  backup: 'Backup do Projeto',
  downloadTheProjectBackupFile: 'Baixar o arquivo de backup do projeto.',
  restoreProject: 'Restaurar Projeto...',
  incorrectUsrPwd: 'Login ou senha incorretos',
  askDeleteUser: 'Você realmente deseja excluir este usuário?',
  askDeleteTemp: 'Você realmente deseja excluir este modelo?',
  askDeleteEnv: 'Você realmente deseja excluir este ambiente?',
  askDeleteInv: 'Você realmente deseja excluir este inventário?',
  askD<PERSON><PERSON><PERSON>ey: 'Você realmente deseja excluir esta chave?',
  askDeleteRepo: 'Você realmente deseja excluir este repositório?',
  askDeleteProj: 'Você realmente deseja excluir este projeto?',
  askDeleteTMem: 'Você realmente deseja excluir este membro da equipe?',
  askDeleteSchedule: 'Você realmente deseja excluir esta agenda?',
  edit: 'Editar',
  nnew: 'Novo',
  keyFormSshKey: 'Chave SSH',
  keyFormLoginPassword: 'Login com senha',
  keyFormNone: 'Nenhum',
  incorrectUrl: 'URL incorreta',
  username: 'Nome de usuário',
  username_required: 'Nome de usuário é obrigatório',
  dashboard: 'Painel',
  history: 'Histórico',
  activity: 'Atividade',
  settings: 'Configurações',
  signIn: 'Entrar',
  password: 'Senha',
  changePassword: 'Alterar senha',
  editUser: 'Editar Usuário',
  newProject: 'Novo Projeto',
  close: 'Fechar',
  newProject2: 'Novo Projeto...',
  demoMode: 'MODO DEMONSTRAÇÃO',
  task: 'Tarefa #{expr}',
  youCanRunAnyTasks: 'Você pode executar quaisquer tarefas',
  youHaveReadonlyAccess: 'Você tem acesso somente leitura',
  taskTemplates: 'Modelos de Tarefa',
  inventory: 'Inventário',
  environment: 'Grupos de Variáveis',
  keyStore: 'Armazenamento de Chaves',
  repositories: 'Repositórios',
  darkMode: 'Modo Escuro',
  team: 'Equipe',
  users: 'Usuários',
  editAccount: 'Editar Conta',
  signOut: 'Sair',
  error: 'Erro',
  refreshPage: 'Atualizar Página',
  relogin: 'Reentrar',
  howToFixSigninIssues: 'Como corrigir problemas de login',
  firstlyYouNeedAccessToTheServerWhereSemaphoreRunni: 'Primeiro, você precisa de acesso ao servidor onde o Semaphore está em execução.',
  executeTheFollowingCommandOnTheServerToSeeExisting: 'Execute o seguinte comando no servidor para ver os usuários existentes:',
  semaphoreUserList: 'lista de usuários semaphore',
  youCanChangePasswordOfExistingUser: 'Você pode alterar a senha do usuário existente:',
  semaphoreUserChangebyloginLoginUser123Password: 'semaphore user change-by-login --login user123 --password {makePasswordExample}',
  orCreateNewAdminUser: 'Ou crie um novo usuário administrador:',
  close2: 'Fechar',
  semaphore: 'SEMAFORO',
  dontHaveAccountOrCantSignIn: 'Não tem conta ou não consegue entrar?',
  password2: 'Senha',
  cancel: 'Cancelar',
  noViews: 'Sem visualizações',
  addView: 'Adicionar visualização',
  editEnvironment: 'Editar Grupo de Variáveis',
  deleteEnvironment: 'Excluir grupo de variáveis',
  newEnvironment: 'Novo Grupo',
  environmentName: 'Nome do Grupo',
  extraVariables: 'Variáveis Extras',
  enterExtraVariablesJson: 'Insira variáveis extras em JSON...',
  environmentVariables: 'Variáveis de Ambiente',
  enterEnvJson: 'Insira JSON do ambiente...',
  environmentAndExtraVariablesMustBeValidJsonExample: 'As variáveis de ambiente e extras devem ser JSON válidos. Exemplo:',
  dashboard2: 'Painel',
  ansibleSemaphore: 'Interface do Semaphore',
  wereSorryButHtmlwebpackpluginoptionstitleDoesntWor: 'Lamentamos, mas <%= htmlWebpackPlugin.options.title %> não funciona corretamente sem JavaScript habilitado. Por favor, habilite-o para continuar.',
  deleteInventory: 'Excluir inventário',
  newInventory: 'Novo Inventário',
  name: 'Nome',
  userCredentials: 'Credenciais do Usuário',
  sudoCredentialsOptional: 'Credenciais Sudo (Opcional)',
  type: 'Tipo',
  pathToInventoryFile: 'Caminho para o arquivo de Inventário',
  enterInventory: 'Insira inventário...',
  staticInventoryExample: 'Exemplo de inventário estático:',
  staticYamlInventoryExample: 'Exemplo de inventário YAML estático:',
  keyName: 'Nome da Chave',
  loginOptional: 'Login (Opcional)',
  usernameOptional: 'Nome de usuário (Opcional)',
  privateKey: 'Chave Privada',
  override: 'Substituir',
  useThisTypeOfKeyForHttpsRepositoriesAndForPlaybook: 'Use este tipo de chave para repositórios HTTPS e para playbooks que usam conexões não-SSH.',
  deleteKey: 'Excluir chave',
  newKey: 'Nova Chave',
  create: 'Criar',
  newTask: 'Nova Tarefa',
  cantDeleteThe: 'Não é possível excluir o {objectTitle}',
  theCantBeDeletedBecauseItUsedByTheResourcesBelow: 'O {objectTitle} não pode ser excluído porque está sendo usado pelos recursos abaixo',
  projectName: 'Nome do Projeto',
  allowAlertsForThisProject: 'Permitir alertas para este projeto',
  telegramChatIdOptional: 'ID do Chat do Telegram (Opcional)',
  maxNumberOfParallelTasksOptional: 'Número máximo de tarefas paralelas (Opcional)',
  deleteRepository: 'Excluir repositório',
  newRepository: 'Novo Repositório',
  urlOrPath: 'URL ou caminho',
  absPath: 'caminho absoluto',
  branch: 'Ramo',
  accessKey: 'Chave de Acesso',
  credentialsToAccessToTheGitRepositoryItShouldBe: 'Credenciais para acessar o repositório Git. Deve ser:',
  ifYouUseGitOrSshUrl: 'se você usar URL Git ou SSH.',
  ifYouUseHttpsOrFileUrl: 'se você usar URL HTTPS ou de arquivo.',
  none: 'Nenhum',
  ssh: 'SSH',
  deleteProject: 'Excluir projeto',
  save: 'Salvar',
  deleteProject2: 'Excluir Projeto',
  onceYouDeleteAProjectThereIsNoGoingBackPleaseBeCer: 'Uma vez que você exclui um projeto, não há como voltar. Por favor, tenha certeza.',

  clear_cache: 'Limpar cache',
  clear_cache_message: 'Excluir todos os arquivos de cache relacionados a este projeto. Esta ação é irreversível.',

  name2: 'Nome *',
  title: 'Título *',
  description: 'Descrição',
  required: 'Obrigatório',
  key: '{expr}',
  surveyVariables: 'Variáveis de Pesquisa',
  addVariable: 'Adicionar variável',
  vaultName: 'ID do Cofre (opcional)',
  vaultNameDefault: 'Apenas um nome `default` (vazio) pode existir',
  vaultNameUnique: 'Deve ser único',
  vaultTypePassword: 'Senha',
  vaultTypeScript: 'Script do Cliente',
  vaultScript: 'Caminho do Script',
  vaultScriptRequired: 'Caminho do Script é obrigatório',
  vaultScriptClientRequired: 'O caminho do script deve terminar com \'-client\' e extensão',
  vaults: 'Cofres',
  vaultAdd: 'Adicionar Cofre',
  vaultRequired: 'Senha do Cofre é obrigatória',
  columns: 'Colunas',
  buildVersion: 'Versão de Construção',
  messageOptional: 'Mensagem (Opcional)',
  debug: 'Depurar',
  dryRun: 'Execução Simulada',
  diff: 'Diferença',
  advanced: 'Avançado',
  hide: 'Ocultar',
  pleaseAllowOverridingCliArgumentInTaskTemplateSett: 'Para permitir a substituição de argumento CLI nas configurações do Modelo de Tarefa',
  cliArgsJsonArrayExampleIMyinventoryshPrivatekeythe: 'Argumentos CLI (array JSON). Exemplo: [ "-i", "@myinventory.sh", "--private-key=/there/id_rsa", "-vvvv" ]',
  started: 'Iniciado',
  author: 'Autor',
  duration: 'Duração',
  stop: 'Parar',
  forceStop: 'Parar Forçadamente',
  raw_log: 'Log Bruto',

  confirmTask: 'Confirmar',
  deleteTeamMember: 'Excluir membro da equipe',
  team2: 'Equipe',
  newTeamMember: 'Novo Membro da Equipe',
  user: 'Usuário',
  administrator: 'Administrador',
  definesStartVersionOfYourArtifactEachRunIncrements: 'Define a versão inicial do seu artefato. Cada execução incrementa a versão do artefato.',
  forMoreInformationAboutBuildingSeeThe: 'Para mais informações sobre construção, veja o',
  taskTemplateReference: 'Referência do Modelo de Tarefa',
  definesWhatArtifactShouldBeDeployedWhenTheTaskRun: 'Define qual artefato deve ser implantado quando a tarefa for executada.',
  forMoreInformationAboutDeployingSeeThe: 'Para mais informações sobre implantação, veja o',
  taskTemplateReference2: 'Referência do Modelo de Tarefa',
  definesAutorunSchedule: 'Define a programação de execução automática.',
  forMoreInformationAboutCronSeeThe: 'Para mais informações sobre cron, veja o',
  cronExpressionFormatReference: 'Referência do formato de expressão cron',
  startVersion: 'Versão Inicial',
  example000: 'Exemplo: 0.0.0',
  buildTemplate: 'Modelo de Construção',
  autorun: 'Execução Automática',
  playbookFilename: 'Caminho para o arquivo do playbook *',
  exampleSiteyml: 'Exemplo: deploy/site.yml',
  inventory2: 'Inventário *',
  repository: 'Repositório',
  environment3: 'Grupo de Variáveis *',
  vaultPassword: 'Senha do Cofre',
  vaultPassword2: 'Senha do Cofre',
  view: 'Visualizar',
  cron: 'Cron',
  iWantToRunATaskByTheCronOnlyForForNewCommitsOfSome: 'Quero executar uma tarefa pelo cron apenas para novos commits de algum repositório',
  repository2: 'Repositório',
  cronChecksNewCommitBeforeRun: 'O cron verifica novos commits antes de executar',
  readThe: 'Leia o',
  toLearnMoreAboutCron: 'para saber mais sobre Cron.',
  suppressSuccessAlerts: 'Suprimir alertas de sucesso',
  cliArgsJsonArrayExampleIMyinventoryshPrivatekeythe2: 'Argumentos CLI (array JSON). Exemplo: [ "-i", "@myinventory.sh", "--private-key=/there/id_rsa", "-vvvv" ]',
  allowCliArgsInTask: 'Argumentos CLI',
  docs: 'documentos',
  editViews: 'Editar Visualizações',
  newTemplate: 'Novo modelo',
  taskTemplates2: 'Modelos de Tarefa',
  all: 'Todos',
  notLaunched: 'Não lançado',
  by: 'por {user_name}',
  editTemplate: 'Editar Modelo',
  newTemplate2: 'Novo Modelo',
  deleteTemplate: 'Excluir modelo',
  playbook: 'Playbook',
  email: 'Email',
  adminUser: 'Usuário administrador',
  sendAlerts: 'Enviar alertas',
  deleteUser: 'Excluir usuário',
  newUser: 'Novo Usuário',
  re: 'Re{getActionButtonTitle}',
  teamMember: '{expr} Membro da Equipe',
  taskId: 'ID da Tarefa',
  version: 'Versão',
  status: 'Status',
  start: 'Iniciar',
  actions: 'Ações',
  alert: 'Alerta',
  admin: 'Admin',
  role: 'Função',
  external: 'Externo',
  time: 'Tempo',
  path: 'Caminho',
  gitUrl: 'URL do Git',
  sshKey: 'Chave SSH',
  lastTask: 'Última Tarefa',
  task2: 'Tarefa',
  build: 'Construir',
  deploy: 'Implantar',
  run: 'Executar',
  add: 'Adicionar',
  password_required: 'Senha é obrigatória',
  name_required: 'Nome é obrigatório',
  user_credentials_required: 'Credenciais do usuário são obrigatórias',
  type_required: 'Tipo é obrigatório',
  path_required: 'Caminho para o arquivo de Inventário é obrigatório',
  private_key_required: 'Chave privada é obrigatória',
  project_name_required: 'Nome do projeto é obrigatório',
  repository_required: 'Repositório é obrigatório',
  branch_required: 'Ramo é obrigatório',
  key_required: 'Chave é obrigatória',
  user_required: 'Usuário é obrigatório',
  build_version_required: 'Versão de construção é obrigatória',
  title_required: 'Título é obrigatório',
  isRequired: 'é obrigatório',
  mustBeInteger: 'Deve ser um número inteiro',
  mustBe0OrGreater: 'Deve ser 0 ou maior',
  start_version_required: 'Versão inicial é obrigatória',
  playbook_filename_required: 'Nome do arquivo do playbook é obrigatório',
  inventory_required: 'Inventário é obrigatório',
  environment_required: 'Ambiente é obrigatório',
  email_required: 'Email é obrigatório',
  build_template_required: 'Modelo de construção é obrigatório',
  Task: 'Tarefa',
  Build: 'Construir',
  Deploy: 'Implantar',
  Run: 'Executar',
  ReBuild: 'Reconstruir',
  ReDeploy: 'Reimplantar',
  ReRun: 'Reexecutar',
  CreateDemoProject: 'Criar Projeto de Demonstração',
  LeaveProject: 'Sair do Projeto',
  integration: 'Integração',
  integrations: 'Integrações',
  NewIntegration: 'Nova Integração',
  EditIntegration: 'Editar Integração',
  DeleteIntegration: 'Excluir Integração',
  DeleteIntegrationMsg: 'Você tem certeza de que deseja excluir esta Integração?',
  AddAlias: 'Adicionar Alias',
  LoadAlias: 'Carregando aliases...',
  globalAlias: 'Usar alias do projeto',
  matcher: 'Correspondente',
  matchType: 'Tipo de Correspondência',
  newMatcher: 'Novo Correspondente',
  matchMethod: 'Método de Comparação',
  matchBodyDataType: 'Tipo de Dados do Corpo',
  extractValue: 'Extrair Valor',
  newExtractedValue: 'Novo Valor Extraído',
  extractedValueSource: 'Fonte do Valor',
  matchKey: 'Chave',
  matchValue: 'Valor',
  matchOn: 'Correspondência em',
  runners: 'Executores',
  newRunner: 'Novo Executor',
  enabled: 'Habilitado',
  scheduleNextRun: 'Próxima execução',
  maxNumberOfParallelTasks: 'Número máximo de tarefas paralelas',
  runnerUsage: 'Uso:',
  runnerDockerCommand: 'Docker:',
  runnerToken: 'Token:',
  editRunner: 'Editar Executor',
  deleteRunner: 'Excluir Executor',
  newRunnerToken: 'Novo Executor',
  askDeleteRunner: 'Você realmente deseja excluir o executor {runner}?',
  project_runners_only_pro: 'Executores em nível de projeto estão disponíveis apenas na versão <b>PRO</b>.',
  foss_runners_limited: 'A versão de código aberto tem funcionalidade limitada; a funcionalidade completa está na versão <b>PRO</b>.',
  learn_more_about_pro: 'Saiba mais',

  projectRestoreResult: 'Resultados da restauração do projeto',
  projectWithNameRestored: 'Projeto {projectName} restaurado com sucesso.',
  pleaseUpdateAccessKeys: 'Por favor, atualize-os antes de executar tarefas.',
  emptyKeysRestored: '{emptyKeys} chaves vazias adicionadas.',
  template: 'Modelo',
  aliasUrlCopied: 'A URL do alias foi copiada para a área de transferência.',
  yes: 'Sim',
  activeTasks: 'Tarefas Ativas',
  taskLocation: 'Localização',
  empty: 'Vazio',
  noValues: 'Sem valores',
  addArg: 'Adicionar argumento',

  status_success: 'Sucesso',
  status_failed: 'Falhou',
  status_stopped: 'Parado',

  api_tokens: 'Tokens de API',

  // Terraform/OpenTofu
  auto_approve: 'Aprovação automática',

  // Ansible
  tag: 'Tag',
  tag_required: 'Tag é obrigatória',
  allowInventoryInTask: 'Inventário',
  allowLimitInTask: 'Limite',
  addLimit: 'Adicionar limite',
  allowDebug: 'Depurar',
  addTag: 'Adicionar tag',
  skipTags: 'Pular tags',
  addSkippedTag: 'Adicionar tag pulada',
  tags: 'Tags',
  limit: 'Limite',

  runner_tag: 'Tag do Executor',
  task_prompts: 'Solicitações',
  template_advanced: 'Opções avançadas',
  template_app_options: 'Opções de {app}',
  template_app_prompts: 'Solicitações de {app}',
  general_settings: 'Geral',
  danger_zone_settings: 'Zona de Perigo',
  project_stats: 'Estatísticas',
  allow_override_branch: 'Ramo',
  template_common_options: 'Opções comuns',
};
