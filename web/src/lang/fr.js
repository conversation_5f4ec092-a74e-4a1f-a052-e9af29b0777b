export default {
  checkInterval: 'intervalleDeVérification',
  newCommitCheckInterval: 'Nouvel intervalle de vérification des commits',
  schedule: 'Calendrier',
  newSchedule: 'Nouveau calendrier',
  deleteSchedule: 'Supprimer le calendrier',
  editSchedule: 'Modifier le calendrier',
  backup: 'Sauvegarde du projet',
  downloadTheProjectBackupFile: 'Télécharger le fichier de sauvegarde du projet.',
  restoreProject: 'Restaurer le projet...',
  incorrectUsrPwd: 'Identifiant ou mot de passe incorrect',
  askDeleteUser: 'Voulez-vous vraiment supprimer cet utilisateur ?',
  askDeleteTemp: 'Voulez-vous vraiment supprimer ce modèle ?',
  askDeleteEnv: 'Voulez-vous vraiment supprimer cet environnement ?',
  askDeleteInv: 'Voulez-vous vraiment supprimer cet inventaire ?',
  askD<PERSON><PERSON><PERSON><PERSON>: 'Voulez-vous vraiment supprimer cette clé ?',
  askDeleteRepo: 'Voulez-vous vraiment supprimer ce dépôt ?',
  askDeleteProj: 'Voulez-vous vraiment supprimer ce projet ?',
  askDeleteTMem: 'Voulez-vous vraiment supprimer ce membre de l\'équipe ?',
  askDeleteSchedule: 'Voulez-vous vraiment supprimer ce calendrier ?',
  edit: 'Modifier',
  nnew: 'Nouveau',
  keyFormSshKey: 'Clé SSH',
  keyFormLoginPassword: 'Connexion avec mot de passe',
  keyFormNone: 'Aucun',
  incorrectUrl: 'URL incorrecte',
  username: 'Nom d\'utilisateur',
  username_required: 'Le nom d\'utilisateur est requis',
  dashboard: 'Tableau de bord',
  history: 'Historique',
  activity: 'Activité',
  settings: 'Paramètres',
  signIn: 'Se connecter',
  password: 'Mot de passe',
  changePassword: 'Changer le mot de passe',
  editUser: 'Modifier l\'utilisateur',
  newProject: 'Nouveau projet',
  close: 'Fermer',
  newProject2: 'Nouveau projet...',
  demoMode: 'MODE DÉMO',
  task: 'Tâche #{expr}',
  youCanRunAnyTasks: 'Vous pouvez exécuter toutes les tâches',
  youHaveReadonlyAccess: 'Vous avez un accès en lecture seule',
  taskTemplates: 'Modèles de tâches',
  inventory: 'Inventaire',
  environment: 'Groupes de variables',
  keyStore: 'Magasin de clés',
  repositories: 'Dépôts',
  darkMode: 'Mode sombre',
  team: 'Équipe',
  users: 'Utilisateurs',
  editAccount: 'Modifier le compte',
  signOut: 'Se déconnecter',
  error: 'Erreur',
  refreshPage: 'Rafraîchir la page',
  relogin: 'Reconnectez-vous',
  howToFixSigninIssues: 'Comment résoudre les problèmes de connexion',
  firstlyYouNeedAccessToTheServerWhereSemaphoreRunni: 'Tout d\'abord, vous devez avoir accès au serveur où Semaphore fonctionne.',
  executeTheFollowingCommandOnTheServerToSeeExisting: 'Exécutez la commande suivante sur le serveur pour voir les utilisateurs existants :',
  semaphoreUserList: 'liste des utilisateurs semaphore',
  youCanChangePasswordOfExistingUser: 'Vous pouvez changer le mot de passe de l\'utilisateur existant :',
  semaphoreUserChangebyloginLoginUser123Password: 'semaphore user change-by-login --login user123 --password {makePasswordExample}',
  orCreateNewAdminUser: 'Ou créez un nouvel utilisateur administrateur :',
  close2: 'Fermer',
  semaphore: 'SEMAPHORE',
  dontHaveAccountOrCantSignIn: 'Vous n\'avez pas de compte ou vous ne pouvez pas vous connecter ?',
  password2: 'Mot de passe',
  cancel: 'Annuler',
  noViews: 'Aucune vue',
  addView: 'Ajouter une vue',
  editEnvironment: 'Modifier le groupe de variables',
  deleteEnvironment: 'Supprimer le groupe de variables',
  newEnvironment: 'Nouveau groupe',
  environmentName: 'Nom du groupe',
  extraVariables: 'Variables supplémentaires',
  enterExtraVariablesJson: 'Entrez les variables supplémentaires JSON...',
  environmentVariables: 'Variables d\'environnement',
  enterEnvJson: 'Entrez le JSON d\'environnement...',
  environmentAndExtraVariablesMustBeValidJsonExample: 'L\'environnement et les variables supplémentaires doivent être un JSON valide. Exemple :',
  dashboard2: 'Tableau de bord',
  ansibleSemaphore: 'Interface Semaphore',
  wereSorryButHtmlwebpackpluginoptionstitleDoesntWor: 'Nous sommes désolés, mais <%= htmlWebpackPlugin.options.title %> ne fonctionne pas correctement sans JavaScript activé. Veuillez l\'activer pour continuer.',
  deleteInventory: 'Supprimer l\'inventaire',
  newInventory: 'Nouvel inventaire',
  name: 'Nom',
  userCredentials: 'Identifiants de l\'utilisateur',
  sudoCredentialsOptional: 'Identifiants sudo (optionnel)',
  type: 'Type',
  pathToInventoryFile: 'Chemin vers le fichier d\'inventaire',
  enterInventory: 'Entrez l\'inventaire...',
  staticInventoryExample: 'Exemple d\'inventaire statique :',
  staticYamlInventoryExample: 'Exemple d\'inventaire YAML statique :',
  keyName: 'Nom de la clé',
  loginOptional: 'Connexion (optionnel)',
  usernameOptional: 'Nom d\'utilisateur (optionnel)',
  privateKey: 'Clé privée',
  override: 'Remplacer',
  useThisTypeOfKeyForHttpsRepositoriesAndForPlaybook: 'Utilisez ce type de clé pour les dépôts HTTPS et pour les playbooks qui utilisent des connexions non-SSH.',
  deleteKey: 'Supprimer la clé',
  newKey: 'Nouvelle clé',
  create: 'Créer',
  newTask: 'Nouvelle tâche',
  cantDeleteThe: 'Impossible de supprimer le {objectTitle}',
  theCantBeDeletedBecauseItUsedByTheResourcesBelow: 'Le {objectTitle} ne peut pas être supprimé car il est utilisé par les ressources ci-dessous',
  projectName: 'Nom du projet',
  allowAlertsForThisProject: 'Autoriser les alertes pour ce projet',
  telegramChatIdOptional: 'ID de chat Telegram (optionnel)',
  maxNumberOfParallelTasksOptional: 'Nombre maximum de tâches parallèles (optionnel)',
  deleteRepository: 'Supprimer le dépôt',
  newRepository: 'Nouveau dépôt',
  urlOrPath: 'URL ou chemin',
  absPath: 'chemin absolu',
  branch: 'Branche',
  accessKey: 'Clé d\'accès',
  credentialsToAccessToTheGitRepositoryItShouldBe: 'Identifiants pour accéder au dépôt Git. Cela devrait être :',
  ifYouUseGitOrSshUrl: 'si vous utilisez l\'URL Git ou SSH.',
  ifYouUseHttpsOrFileUrl: 'si vous utilisez l\'URL HTTPS ou de fichier.',
  none: 'Aucun',
  ssh: 'SSH',
  deleteProject: 'Supprimer le projet',
  save: 'Enregistrer',
  deleteProject2: 'Supprimer le projet',
  onceYouDeleteAProjectThereIsNoGoingBackPleaseBeCer: 'Une fois que vous avez supprimé un projet, il n\'y a pas de retour en arrière. Veuillez en être certain.',

  clear_cache: 'Effacer le cache',
  clear_cache_message: 'Supprimer tous les fichiers de cache liés à ce projet. Cette action est irréversible.',

  name2: 'Nom *',
  title: 'Titre *',
  description: 'Description',
  required: 'Requis',
  key: '{expr}',
  surveyVariables: 'Variables d\'enquête',
  addVariable: 'Ajouter une variable',
  vaultName: 'ID de coffre (optionnel)',
  vaultNameDefault: 'Un seul nom `default` (vide) peut exister',
  vaultNameUnique: 'Doit être unique',
  vaultTypePassword: 'Mot de passe',
  vaultTypeScript: 'Script client',
  vaultScript: 'Chemin du script',
  vaultScriptRequired: 'Le chemin du script est requis',
  vaultScriptClientRequired: 'Le chemin du script doit se terminer par \'-client\' et une extension',
  vaults: 'Coffres',
  vaultAdd: 'Ajouter un coffre',
  vaultRequired: 'Le mot de passe du coffre est requis',
  columns: 'Colonnes',
  buildVersion: 'Version de construction',
  messageOptional: 'Message (optionnel)',
  debug: 'Déboguer',
  dryRun: 'Exécution à blanc',
  diff: 'Diff',
  advanced: 'Avancé',
  hide: 'Cacher',
  pleaseAllowOverridingCliArgumentInTaskTemplateSett: 'Pour autoriser le remplacement de l\'argument CLI dans les paramètres du modèle de tâche',
  cliArgsJsonArrayExampleIMyinventoryshPrivatekeythe: 'Arguments CLI (tableau JSON). Exemple : [ "-i", "@myinventory.sh", "--private-key=/there/id_rsa", "-vvvv" ]',
  started: 'Démarré',
  author: 'Auteur',
  duration: 'Durée',
  stop: 'Arrêter',
  forceStop: 'Arrêt forcé',
  raw_log: 'Journal brut',

  confirmTask: 'Confirmer',
  deleteTeamMember: 'Supprimer le membre de l\'équipe',
  team2: 'Équipe',
  newTeamMember: 'Nouveau membre de l\'équipe',
  user: 'Utilisateur',
  administrator: 'Administrateur',
  definesStartVersionOfYourArtifactEachRunIncrements: 'Définit la version de départ de votre artefact. Chaque exécution incrémente la version de l\'artefact.',
  forMoreInformationAboutBuildingSeeThe: 'Pour plus d\'informations sur la construction, voir le',
  taskTemplateReference: 'Référence du modèle de tâche',
  definesWhatArtifactShouldBeDeployedWhenTheTaskRun: 'Définit quel artefact doit être déployé lorsque la tâche s\'exécute.',
  forMoreInformationAboutDeployingSeeThe: 'Pour plus d\'informations sur le déploiement, voir le',
  taskTemplateReference2: 'Référence du modèle de tâche',
  definesAutorunSchedule: 'Définit le calendrier d\'exécution automatique.',
  forMoreInformationAboutCronSeeThe: 'Pour plus d\'informations sur cron, voir le',
  cronExpressionFormatReference: 'Référence du format d\'expression cron',
  startVersion: 'Version de départ',
  example000: 'Exemple : 0.0.0',
  buildTemplate: 'Modèle de construction',
  autorun: 'Exécution automatique',
  playbookFilename: 'Chemin vers le fichier playbook *',
  exampleSiteyml: 'Exemple : deploy/site.yml',
  inventory2: 'Inventaire *',
  repository: 'Dépôt',
  environment3: 'Groupe de variables *',
  vaultPassword: 'Mot de passe du coffre',
  vaultPassword2: 'Mot de passe du coffre',
  view: 'Vue',
  cron: 'Cron',
  iWantToRunATaskByTheCronOnlyForForNewCommitsOfSome: 'Je veux exécuter une tâche par cron uniquement pour les nouveaux commits d\'un dépôt',
  repository2: 'Dépôt',
  cronChecksNewCommitBeforeRun: 'Cron vérifie les nouveaux commits avant d\'exécuter',
  readThe: 'Lisez le',
  toLearnMoreAboutCron: 'pour en savoir plus sur Cron.',
  suppressSuccessAlerts: 'Supprimer les alertes de succès',
  cliArgsJsonArrayExampleIMyinventoryshPrivatekeythe2: 'Arguments CLI (tableau JSON). Exemple : [ "-i", "@myinventory.sh", "--private-key=/there/id_rsa", "-vvvv" ]',
  allowCliArgsInTask: 'Arguments CLI',
  docs: 'docs',
  editViews: 'Modifier les vues',
  newTemplate: 'Nouveau modèle',
  taskTemplates2: 'Modèles de tâches',
  all: 'Tous',
  notLaunched: 'Non lancé',
  by: 'par {user_name}',
  editTemplate: 'Modifier le modèle',
  newTemplate2: 'Nouveau modèle',
  deleteTemplate: 'Supprimer le modèle',
  playbook: 'Playbook',
  email: 'Email',
  adminUser: 'Utilisateur administrateur',
  sendAlerts: 'Envoyer des alertes',
  deleteUser: 'Supprimer l\'utilisateur',
  newUser: 'Nouvel utilisateur',
  re: 'Re{getActionButtonTitle}',
  teamMember: '{expr} membre de l\'équipe',
  taskId: 'ID de tâche',
  version: 'Version',
  status: 'Statut',
  start: 'Démarrer',
  actions: 'Actions',
  alert: 'Alerte',
  admin: 'Admin',
  role: 'Rôle',
  external: 'Externe',
  time: 'Temps',
  path: 'Chemin',
  gitUrl: 'URL Git',
  sshKey: 'Clé SSH',
  lastTask: 'Dernière tâche',
  task2: 'Tâche',
  build: 'Construire',
  deploy: 'Déployer',
  run: 'Exécuter',
  add: 'Ajouter',
  password_required: 'Le mot de passe est requis',
  name_required: 'Le nom est requis',
  user_credentials_required: 'Les identifiants de l\'utilisateur sont requis',
  type_required: 'Le type est requis',
  path_required: 'Le chemin vers le fichier d\'inventaire est requis',
  private_key_required: 'La clé privée est requise',
  project_name_required: 'Le nom du projet est requis',
  repository_required: 'Le dépôt est requis',
  branch_required: 'La branche est requise',
  key_required: 'La clé est requise',
  user_required: 'L\'utilisateur est requis',
  build_version_required: 'La version de construction est requise',
  title_required: 'Le titre est requis',
  isRequired: 'est requis',
  mustBeInteger: 'Doit être un entier',
  mustBe0OrGreater: 'Doit être 0 ou plus',
  start_version_required: 'La version de départ est requise',
  playbook_filename_required: 'Le nom du fichier playbook est requis',
  inventory_required: 'L\'inventaire est requis',
  environment_required: 'L\'environnement est requis',
  email_required: 'L\'email est requis',
  build_template_required: 'Le modèle de construction est requis',
  Task: 'Tâche',
  Build: 'Construire',
  Deploy: 'Déployer',
  Run: 'Exécuter',
  ReBuild: 'Reconstruire',
  ReDeploy: 'Redéployer',
  ReRun: 'Relancer',
  CreateDemoProject: 'Créer un projet de démonstration',
  LeaveProject: 'Quitter le projet',
  integration: 'Intégration',
  integrations: 'Intégrations',
  NewIntegration: 'Nouvelle intégration',
  EditIntegration: 'Modifier l\'intégration',
  DeleteIntegration: 'Supprimer l\'intégration',
  DeleteIntegrationMsg: 'Êtes-vous sûr de vouloir supprimer cette intégration ?',
  AddAlias: 'Ajouter un alias',
  LoadAlias: 'Chargement des alias...',
  globalAlias: 'Utiliser l\'alias du projet',
  matcher: 'Correspondant',
  matchType: 'Type de correspondance',
  newMatcher: 'Nouveau correspondant',
  matchMethod: 'Méthode de comparaison',
  matchBodyDataType: 'Type de données du corps',
  extractValue: 'Extraire la valeur',
  newExtractedValue: 'Nouvelle valeur extraite',
  extractedValueSource: 'Source de valeur',
  matchKey: 'Clé',
  matchValue: 'Valeur',
  matchOn: 'Correspondre sur',
  runners: 'Exécuteurs',
  newRunner: 'Nouvel exécuteur',
  enabled: 'Activé',
  scheduleNextRun: 'Prochaine exécution',
  maxNumberOfParallelTasks: 'Nombre maximum de tâches parallèles',
  runnerUsage: 'Utilisation :',
  runnerDockerCommand: 'Docker :',
  runnerToken: 'Jeton :',
  editRunner: 'Modifier l\'exécuteur',
  deleteRunner: 'Supprimer l\'exécuteur',
  newRunnerToken: 'Nouvel exécuteur',
  askDeleteRunner: 'Voulez-vous vraiment supprimer l\'exécuteur {runner} ?',
  project_runners_only_pro: 'Les exécuteurs au niveau du projet ne sont disponibles que dans la version <b>PRO</b>.',
  foss_runners_limited: 'La version open-source a des fonctionnalités limitées ; la fonctionnalité complète est dans la version <b>PRO</b>.',
  learn_more_about_pro: 'En savoir plus',

  projectRestoreResult: 'Résultats de la restauration du projet',
  projectWithNameRestored: 'Projet {projectName} restauré avec succès.',
  pleaseUpdateAccessKeys: 'Veuillez les mettre à jour avant d\'exécuter des tâches.',
  emptyKeysRestored: '{emptyKeys} clés vides ajoutées.',
  template: 'Modèle',
  aliasUrlCopied: 'L\'URL de l\'alias a été copiée dans le presse-papiers.',
  yes: 'Oui',
  activeTasks: 'Tâches actives',
  taskLocation: 'Emplacement',
  empty: 'Vide',
  noValues: 'Aucune valeur',
  addArg: 'Ajouter un argument',

  status_success: 'Succès',
  status_failed: 'Échoué',
  status_stopped: 'Arrêté',

  api_tokens: 'Jetons API',

  // Terraform/OpenTofu
  auto_approve: 'Approbation automatique',

  // Ansible
  tag: 'Étiquette',
  tag_required: 'L\'étiquette est requise',
  allowInventoryInTask: 'Inventaire',
  allowLimitInTask: 'Limite',
  addLimit: 'Ajouter une limite',
  allowDebug: 'Déboguer',
  addTag: 'Ajouter une étiquette',
  skipTags: 'Ignorer les étiquettes',
  addSkippedTag: 'Ajouter une étiquette ignorée',
  tags: 'Étiquettes',
  limit: 'Limite',

  runner_tag: 'Étiquette de l\'exécuteur',
  task_prompts: 'Invites',
  template_advanced: 'Options avancées',
  template_app_options: 'Options {app}',
  template_app_prompts: 'Invites {app}',
  general_settings: 'Général',
  danger_zone_settings: 'Zone de danger',
  project_stats: 'Statistiques',
  allow_override_branch: 'Branche',
  template_common_options: 'Options communes',
};
