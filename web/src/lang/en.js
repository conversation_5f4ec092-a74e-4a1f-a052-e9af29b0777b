export default {
  checkInterval: 'checkInterval',
  newCommitCheckInterval: 'New commit check interval',
  schedule: 'Schedule',
  newSchedule: 'New Schedule',
  deleteSchedule: 'Delete Schedule',
  editSchedule: 'Edit Schedule',
  backup: 'Backup Project',
  downloadTheProjectBackupFile: 'Download the project backup file.',
  restoreProject: 'Restore Project...',
  incorrectUsrPwd: 'Incorrect login or password',
  askD<PERSON><PERSON><PERSON><PERSON>: 'Do you really want to delete this user?',
  ask<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'Do you really want to delete this template?',
  askD<PERSON>teEnv: 'Do you really want to delete this environment?',
  askDeleteInv: 'Do you really want to delete this inventor?',
  ask<PERSON><PERSON><PERSON><PERSON><PERSON>: 'Do you really want to delete this key?',
  ask<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: 'Do you really want to delete this repository?',
  ask<PERSON><PERSON><PERSON>Proj: 'Do you really want to delete this project?',
  ask<PERSON><PERSON><PERSON><PERSON><PERSON>: 'Do you really want to delete this team member?',
  askDeleteSchedule: 'Do you really want to delete this schedule?',
  edit: 'Edit',
  nnew: 'New',
  keyFormSshKey: 'SSH Key',
  keyFormLoginPassword: 'Login with password',
  keyFormNone: 'None',
  incorrectUrl: 'Incorrect URL',
  username: 'Username',
  username_required: 'Username is required',
  dashboard: 'Dashboard',
  history: 'History',
  activity: 'Activity',
  settings: 'Settings',
  signIn: 'Sign In',
  password: 'Password',
  changePassword: 'Change password',
  editUser: 'Edit User',
  newProject: 'New Project',
  close: 'Close',
  newProject2: 'New Project...',
  demoMode: 'DEMO MODE',
  task: 'Task #{expr}',
  youCanRunAnyTasks: 'You can run any tasks',
  youHaveReadonlyAccess: 'You have read-only access',
  taskTemplates: 'Task Templates',
  inventory: 'Inventory',
  environment: 'Variable Groups',
  keyStore: 'Key Store',
  repositories: 'Repositories',
  darkMode: 'Dark Mode',
  team: 'Team',
  users: 'Users',
  editAccount: 'Edit Account',
  signOut: 'Sign Out',
  error: 'Error',
  refreshPage: 'Refresh Page',
  relogin: 'Relogin',
  howToFixSigninIssues: 'How to fix sign-in issues',
  firstlyYouNeedAccessToTheServerWhereSemaphoreRunni: 'Firstly, you need access to the server where Semaphore running.',
  executeTheFollowingCommandOnTheServerToSeeExisting: 'Execute the following command on the server to see existing users:',
  semaphoreUserList: 'semaphore user list',
  youCanChangePasswordOfExistingUser: 'You can change password of existing user:',
  semaphoreUserChangebyloginLoginUser123Password: 'semaphore user change-by-login --login user123 --password {makePasswordExample}',
  orCreateNewAdminUser: 'Or create new admin user:',
  close2: 'Close',
  semaphore: 'SEMAPHORE',
  dontHaveAccountOrCantSignIn: 'Don\'t have account or can\'t sign in?',
  password2: 'Password',
  cancel: 'Cancel',
  noViews: 'No views',
  addView: 'Add view',
  editEnvironment: 'Edit Variable Group',
  deleteEnvironment: 'Delete variable group',
  newEnvironment: 'New Group',
  environmentName: 'Group Name',
  extraVariables: 'Extra variables',
  enterExtraVariablesJson: 'Enter extra variables JSON...',
  environmentVariables: 'Environment variables',
  enterEnvJson: 'Enter env JSON...',
  environmentAndExtraVariablesMustBeValidJsonExample: 'Environment and extra variables must be valid JSON. Example:',
  dashboard2: 'Dashboard',
  ansibleSemaphore: 'Semaphore UI',
  wereSorryButHtmlwebpackpluginoptionstitleDoesntWor: 'We\'re sorry but <%= htmlWebpackPlugin.options.title %> doesn\'t work properly without JavaScript enabled. Please enable it to continue.',
  deleteInventory: 'Delete inventory',
  newInventory: 'New Inventory',
  name: 'Name',
  userCredentials: 'User Credentials',
  sudoCredentialsOptional: 'Sudo Credentials (Optional)',
  type: 'Type',
  pathToInventoryFile: 'Path to Inventory file',
  enterInventory: 'Enter inventory...',
  staticInventoryExample: 'Static inventory example:',
  staticYamlInventoryExample: 'Static YAML inventory example:',
  keyName: 'Key Name',
  loginOptional: 'Login (Optional)',
  usernameOptional: 'Username (Optional)',
  privateKey: 'Private Key',
  override: 'Override',
  useThisTypeOfKeyForHttpsRepositoriesAndForPlaybook: 'Use this type of key for HTTPS repositories and for playbooks which use non-SSH connections.',
  deleteKey: 'Delete key',
  newKey: 'New Key',
  create: 'Create',
  newTask: 'New Task',
  cantDeleteThe: 'Can\'t delete the {objectTitle}',
  theCantBeDeletedBecauseItUsedByTheResourcesBelow: 'The {objectTitle} can\'t be deleted because it used by the resources below',
  projectName: 'Project Name',
  allowAlertsForThisProject: 'Allow alerts for this project',
  telegramChatIdOptional: 'Telegram Chat ID (Optional)',
  maxNumberOfParallelTasksOptional: 'Max number of parallel tasks (Optional)',
  deleteRepository: 'Delete repository',
  newRepository: 'New Repository',
  urlOrPath: 'URL or path',
  absPath: 'abs. path',
  branch: 'Branch',
  accessKey: 'Access Key',
  credentialsToAccessToTheGitRepositoryItShouldBe: 'Credentials to access to the Git repository. It should be:',
  ifYouUseGitOrSshUrl: 'if you use Git or SSH URL.',
  ifYouUseHttpsOrFileUrl: 'if you use HTTPS or file URL.',
  none: 'None',
  ssh: 'SSH',
  deleteProject: 'Delete project',
  save: 'Save',
  deleteProject2: 'Delete Project',
  onceYouDeleteAProjectThereIsNoGoingBackPleaseBeCer: 'Once you delete a project, there is no going back. Please be certain.',

  clear_cache: 'Clear cache',
  clear_cache_message: 'Delete all cache files related to this project. This action is irreversible.',

  name2: 'Name *',
  title: 'Title *',
  description: 'Description',
  required: 'Required',
  key: '{expr}',
  surveyVariables: 'Survey Variables',
  addVariable: 'Add variable',
  vaultName: 'Vault ID (optional)',
  vaultNameDefault: 'Only one `default` (empty) name may exist',
  default_value: 'Default value',
  vaultNameUnique: 'Must be unique',
  vaultTypePassword: 'Password',
  vaultTypeScript: 'Client Script',
  vaultScript: 'Script Path',
  vaultScriptRequired: 'Script Path is required',
  vaultScriptClientRequired: 'Script path must end with \'-client\' and extension',
  vaults: 'Vaults',
  vaultAdd: 'Add Vault',
  vaultRequired: 'Vault Password is required',
  columns: 'Columns',
  buildVersion: 'Build Version',
  messageOptional: 'Message (Optional)',
  debug: 'Debug',
  dryRun: 'Dry Run',
  diff: 'Diff',
  advanced: 'Advanced',
  hide: 'Hide',
  pleaseAllowOverridingCliArgumentInTaskTemplateSett: 'To allow overriding CLI argument in Task Template settings',
  cliArgsJsonArrayExampleIMyinventoryshPrivatekeythe: 'CLI Args (JSON array). Example: [ "-i", "@myinventory.sh", "--private-key=/there/id_rsa", "-vvvv" ]',
  started: 'Started',
  author: 'Author',
  duration: 'Duration',
  stop: 'Stop',
  forceStop: 'Force Stop',
  raw_log: 'Raw log',

  confirmTask: 'Confirm',
  deleteTeamMember: 'Delete team member',
  team2: 'Team',
  newTeamMember: 'New Team Member',
  user: 'User',
  administrator: 'Administrator',
  definesStartVersionOfYourArtifactEachRunIncrements: 'Defines start version of your artifact. Each run increments the artifact version.',
  forMoreInformationAboutBuildingSeeThe: 'For more information about building, see the',
  taskTemplateReference: 'Task Template reference',
  definesWhatArtifactShouldBeDeployedWhenTheTaskRun: 'Defines what artifact should be deployed when the task run.',
  forMoreInformationAboutDeployingSeeThe: 'For more information about deploying, see the',
  taskTemplateReference2: 'Task Template reference',
  definesAutorunSchedule: 'Defines autorun schedule.',
  forMoreInformationAboutCronSeeThe: 'For more information about cron, see the',
  cronExpressionFormatReference: 'Cron expression format reference',
  startVersion: 'Start Version',
  example000: 'Example: 0.0.0',
  buildTemplate: 'Build Template',
  autorun: 'Autorun',
  playbookFilename: 'Path to playbook file *',
  exampleSiteyml: 'Example: deploy/site.yml',
  inventory2: 'Inventory *',
  repository: 'Repository',
  environment3: 'Variable Group *',
  vaultPassword: 'Vault Password',
  vaultPassword2: 'Vault Password',
  view: 'View',
  cron: 'Cron',
  iWantToRunATaskByTheCronOnlyForForNewCommitsOfSome: 'I want to run a task by the cron only for for new commits of some repository',
  repository2: 'Repository',
  cronChecksNewCommitBeforeRun: 'Cron checks new commit before run',
  readThe: 'Read the',
  toLearnMoreAboutCron: 'to learn more about Cron.',
  suppressSuccessAlerts: 'Suppress success alerts',
  cliArgsJsonArrayExampleIMyinventoryshPrivatekeythe2: 'CLI Args (JSON array). Example: [ "-i", "@myinventory.sh", "--private-key=/there/id_rsa", "-vvvv" ]',
  allowCliArgsInTask: 'CLI args',
  docs: 'docs',
  editViews: 'Edit Views',
  newTemplate: 'New template',
  taskTemplates2: 'Task Templates',
  all: 'All',
  notLaunched: 'Not launched',
  by: 'by {user_name}',
  editTemplate: 'Edit Template',
  newTemplate2: 'New Template',
  deleteTemplate: 'Delete template',
  playbook: 'Playbook',
  email: 'Email',
  adminUser: 'Admin user',
  sendAlerts: 'Send alerts',
  deleteUser: 'Delete user',
  newUser: 'New User',
  re: 'Re{getActionButtonTitle}',
  teamMember: '{expr} Team Member',
  taskId: 'Task ID',
  version: 'Version',
  status: 'Status',
  start: 'Start',
  actions: 'Actions',
  alert: 'Alert',
  admin: 'Admin',
  role: 'Role',
  external: 'External',
  time: 'Time',
  path: 'Path',
  gitUrl: 'Git URL',
  sshKey: 'SSH Key',
  lastTask: 'Last Task',
  task2: 'Task',
  build: 'Build',
  deploy: 'Deploy',
  run: 'Run',
  add: 'Add',
  password_required: 'Password is required',
  name_required: 'Name is required',
  user_credentials_required: 'User credentials are required',
  type_required: 'Type is required',
  path_required: 'Path to Inventory file is required',
  private_key_required: 'Private key is required',
  project_name_required: 'Project name is required',
  repository_required: 'Repository is required',
  branch_required: 'Branch is required',
  key_required: 'Key is required',
  user_required: 'User is required',
  build_version_required: 'Build version is required',
  title_required: 'Title is required',
  isRequired: 'is required',
  mustBeInteger: 'Must be integer',
  mustBe0OrGreater: 'Must be 0 or greater',
  start_version_required: 'Start version is required',
  playbook_filename_required: 'Playbook filename is required',
  inventory_required: 'Inventory is required',
  environment_required: 'Environment is required',
  email_required: 'Email is required',
  build_template_required: 'Build template is required',
  Task: 'Task',
  Build: 'Build',
  Deploy: 'Deploy',
  Run: 'Run',
  ReBuild: 'Rebuild',
  ReDeploy: 'Redeploy',
  ReRun: 'Rerun',
  CreateDemoProject: 'Create Demo Project',
  LeaveProject: 'Leave Project',
  integration: 'Integration',
  integrations: 'Integrations',
  NewIntegration: 'New Integration',
  EditIntegration: 'Edit Integration',
  DeleteIntegration: 'Delete Integration',
  DeleteIntegrationMsg: 'Are you sure you want to delete this Integration?',
  AddAlias: 'Add Alias',
  LoadAlias: 'Loading aliases...',
  globalAlias: 'Use project alias',
  matcher: 'Matcher',
  matchType: 'Match Type',
  newMatcher: 'New Matcher',
  matchMethod: 'Comparison Method',
  matchBodyDataType: 'Body Data Type',
  extractValue: 'Extract Value',
  newExtractedValue: 'New Extracted Value',
  extractedValueSource: 'Value Source',
  matchKey: 'Key',
  matchValue: 'Value',
  matchOn: 'Match on',
  runners: 'Runners',
  newRunner: 'New Runner',
  enabled: 'Enabled',
  scheduleNextRun: 'Next run',
  maxNumberOfParallelTasks: 'Maximum parallel tasks',
  runnerUsage: 'Usage:',
  runnerDockerCommand: 'Docker:',
  runnerToken: 'Token:',
  editRunner: 'Edit Runner',
  deleteRunner: 'Delete Runner',
  newRunnerToken: 'New Runner',
  askDeleteRunner: 'Do you really want to delete runner {runner}?',
  project_runners_only_pro: 'Project-level runners are only available in the <b>PRO</b> version.',
  foss_runners_limited: 'The open-source version has limited functionality; full functionality is in the <b>PRO</b> version.',
  learn_more_about_pro: 'Learn more',

  projectRestoreResult: 'Project restore results',
  projectWithNameRestored: 'Project {projectName} successfully restored.',
  pleaseUpdateAccessKeys: 'Please update them before running tasks.',
  emptyKeysRestored: '{emptyKeys} empty keys added.',
  template: 'Template',
  aliasUrlCopied: 'The alias URL has been copied to the clipboard.',
  yes: 'Yes',
  activeTasks: 'Active Tasks',
  taskLocation: 'Location',
  empty: 'Empty',
  noValues: 'No values',
  addArg: 'Add arg',

  status_success: 'Success',
  status_failed: 'Failed',
  status_stopped: 'Stopped',

  api_tokens: 'API Tokens',

  // Terraform/OpenTofu
  auto_approve: 'Auto approve',

  // Ansible
  tag: 'Tag',
  tag_required: 'Tag is required',
  allowInventoryInTask: 'Inventory',
  allowLimitInTask: 'Limit',
  addLimit: 'Add limit',
  allowDebug: 'Debug',
  addTag: 'Add tag',
  skipTags: 'Skip tags',
  addSkippedTag: 'Add skipped tag',
  tags: 'Tags',
  limit: 'Limit',

  runner_tag: 'Runner tag',
  task_prompts: 'Prompts',
  template_advanced: 'Advanced options',
  template_app_options: '{app} options',
  template_app_prompts: '{app} prompts',
  general_settings: 'General',
  danger_zone_settings: 'Danger Zone',
  project_stats: 'Stats',
  allow_override_branch: 'Branch',
  template_common_options: 'Common options',

  template_tasks: 'Tasks',
  template_details: 'Details',
  template_tf_workspaces: 'Workspaces',
};
