export default {
  checkInterval: 'チェック間隔',
  newCommitCheckInterval: '新しいコミットのチェック間隔',
  schedule: 'スケジュール',
  newSchedule: '新しいスケジュール',
  deleteSchedule: 'スケジュールを削除',
  editSchedule: 'スケジュールを編集',
  backup: 'プロジェクトのバックアップ',
  downloadTheProjectBackupFile: 'プロジェクトのバックアップファイルをダウンロードします。',
  restoreProject: 'プロジェクトを復元...',
  incorrectUsrPwd: 'ログインまたはパスワードが正しくありません',
  askDeleteUser: '本当にこのユーザーを削除しますか？',
  askDeleteTemp: '本当にこのテンプレートを削除しますか？',
  askDeleteEnv: '本当にこの環境を削除しますか？',
  askDeleteInv: '本当にこのインベントリを削除しますか？',
  askDeleteKey: '本当にこのキーを削除しますか？',
  askDeleteRepo: '本当にこのリポジトリを削除しますか？',
  askDeleteProj: '本当にこのプロジェクトを削除しますか？',
  askDeleteTMem: '本当にこのチームメンバーを削除しますか？',
  askDeleteSchedule: '本当にこのスケジュールを削除しますか？',
  edit: '編集',
  nnew: '新しい',
  keyFormSshKey: 'SSHキー',
  keyFormLoginPassword: 'パスワードでログイン',
  keyFormNone: 'なし',
  incorrectUrl: '不正なURL',
  username: 'ユーザー名',
  username_required: 'ユーザー名は必須です',
  dashboard: 'ダッシュボード',
  history: '履歴',
  activity: 'アクティビティ',
  settings: '設定',
  signIn: 'サインイン',
  password: 'パスワード',
  changePassword: 'パスワードを変更',
  editUser: 'ユーザーを編集',
  newProject: '新しいプロジェクト',
  close: '閉じる',
  newProject2: '新しいプロジェクト...',
  demoMode: 'デモモード',
  task: 'タスク #{expr}',
  youCanRunAnyTasks: '任意のタスクを実行できます',
  youHaveReadonlyAccess: '読み取り専用アクセスがあります',
  taskTemplates: 'タスクテンプレート',
  inventory: 'インベントリ',
  environment: '変数グループ',
  keyStore: 'キーのストア',
  repositories: 'リポジトリ',
  darkMode: 'ダークモード',
  team: 'チーム',
  users: 'ユーザー',
  editAccount: 'アカウントを編集',
  signOut: 'サインアウト',
  error: 'エラー',
  refreshPage: 'ページを更新',
  relogin: '再ログイン',
  howToFixSigninIssues: 'サインインの問題を修正する方法',
  firstlyYouNeedAccessToTheServerWhereSemaphoreRunni: 'まず、Semaphoreが実行されているサーバーにアクセスする必要があります。',
  executeTheFollowingCommandOnTheServerToSeeExisting: 'サーバーで次のコマンドを実行して、既存のユーザーを確認します:',
  semaphoreUserList: 'semaphore user list',
  youCanChangePasswordOfExistingUser: '既存のユーザーのパスワードを変更できます:',
  semaphoreUserChangebyloginLoginUser123Password: 'semaphore user change-by-login --login user123 --password {makePasswordExample}',
  orCreateNewAdminUser: 'または新しい管理者ユーザーを作成します:',
  close2: '閉じる',
  semaphore: 'セマフォ',
  dontHaveAccountOrCantSignIn: 'アカウントがないか、サインインできませんか？',
  password2: 'パスワード',
  cancel: 'キャンセル',
  noViews: 'ビューがありません',
  addView: 'ビューを追加',
  editEnvironment: '変数グループを編集',
  deleteEnvironment: '変数グループを削除',
  newEnvironment: '新しいグループ',
  environmentName: 'グループ名',
  extraVariables: '追加の変数',
  enterExtraVariablesJson: '追加の変数JSONを入力...',
  environmentVariables: '環境変数',
  enterEnvJson: 'env JSONを入力...',
  environmentAndExtraVariablesMustBeValidJsonExample: '環境変数と追加の変数は有効なJSONでなければなりません。例:',
  dashboard2: 'ダッシュボード',
  ansibleSemaphore: 'セマフォUI',
  wereSorryButHtmlwebpackpluginoptionstitleDoesntWor: '申し訳ありませんが、<%= htmlWebpackPlugin.options.title %>はJavaScriptが有効でないと正しく動作しません。続行するには有効にしてください。',
  deleteInventory: 'インベントリを削除',
  newInventory: '新しいインベントリ',
  name: '名前',
  userCredentials: 'ユーザー資格情報',
  sudoCredentialsOptional: 'Sudo資格情報（オプション）',
  type: 'タイプ',
  pathToInventoryFile: 'インベントリファイルへのパス',
  enterInventory: 'インベントリを入力...',
  staticInventoryExample: '静的インベントリの例:',
  staticYamlInventoryExample: '静的YAMLインベントリの例:',
  keyName: 'キー名',
  loginOptional: 'ログイン（オプション）',
  usernameOptional: 'ユーザー名（オプション）',
  privateKey: '秘密鍵',
  override: '上書き',
  useThisTypeOfKeyForHttpsRepositoriesAndForPlaybook: 'HTTPSリポジトリおよび非SSH接続を使用するプレイブックにこのタイプのキーを使用します。',
  deleteKey: 'キーを削除',
  newKey: '新しいキー',
  create: '作成',
  newTask: '新しいタスク',
  cantDeleteThe: '{objectTitle}を削除できません',
  theCantBeDeletedBecauseItUsedByTheResourcesBelow: '{objectTitle}は以下のリソースで使用されているため削除できません',
  projectName: 'プロジェクト名',
  allowAlertsForThisProject: 'このプロジェクトのアラートを許可',
  telegramChatIdOptional: 'TelegramチャットID（オプション）',
  maxNumberOfParallelTasksOptional: '最大並列タスク数（オプション）',
  deleteRepository: 'リポジトリを削除',
  newRepository: '新しいリポジトリ',
  urlOrPath: 'URLまたはパス',
  absPath: '絶対パス',
  branch: 'ブランチ',
  accessKey: 'アクセスキー',
  credentialsToAccessToTheGitRepositoryItShouldBe: 'Gitリポジトリにアクセスするための資格情報。次のようにする必要があります:',
  ifYouUseGitOrSshUrl: 'GitまたはSSH URLを使用する場合。',
  ifYouUseHttpsOrFileUrl: 'HTTPSまたはファイルURLを使用する場合。',
  none: 'なし',
  ssh: 'SSH',
  deleteProject: 'プロジェクトを削除',
  save: '保存',
  deleteProject2: 'プロジェクトを削除',
  onceYouDeleteAProjectThereIsNoGoingBackPleaseBeCer: 'プロジェクトを削除すると、元に戻すことはできません。確信してください。',

  clear_cache: 'キャッシュをクリア',
  clear_cache_message: 'このプロジェクトに関連するすべてのキャッシュファイルを削除します。この操作は元に戻せません。',

  name2: '名前 *',
  title: 'タイトル *',
  description: '説明',
  required: '必須',
  key: '{expr}',
  surveyVariables: '調査変数',
  addVariable: '変数を追加',
  vaultName: 'ボールトID（オプション）',
  vaultNameDefault: '唯一の`default`（空）名が存在することができます',
  vaultNameUnique: '一意でなければなりません',
  vaultTypePassword: 'パスワード',
  vaultTypeScript: 'クライアントスクリプト',
  vaultScript: 'スクリプトパス',
  vaultScriptRequired: 'スクリプトパスは必須です',
  vaultScriptClientRequired: 'スクリプトパスは\'-client\'で終わり、拡張子が必要です',
  vaults: 'ボールト',
  vaultAdd: 'ボールトを追加',
  vaultRequired: 'ボールトパスワードは必須です',
  columns: '列',
  buildVersion: 'ビルドバージョン',
  messageOptional: 'メッセージ（オプション）',
  debug: 'デバッグ',
  dryRun: 'ドライラン',
  diff: '差分',
  advanced: '高度な',
  hide: '隠す',
  pleaseAllowOverridingCliArgumentInTaskTemplateSett: 'タスクテンプレート設定でCLI引数の上書きを許可するために',
  cliArgsJsonArrayExampleIMyinventoryshPrivatekeythe: 'CLI引数（JSON配列）。例: [ "-i", "@myinventory.sh", "--private-key=/there/id_rsa", "-vvvv" ]',
  started: '開始',
  author: '著者',
  duration: '期間',
  stop: '停止',
  forceStop: '強制停止',
  raw_log: '生ログ',

  confirmTask: '確認',
  deleteTeamMember: 'チームメンバーを削除',
  team2: 'チーム',
  newTeamMember: '新しいチームメンバー',
  user: 'ユーザー',
  administrator: '管理者',
  definesStartVersionOfYourArtifactEachRunIncrements: 'アーティファクトの開始バージョンを定義します。各実行でアーティファクトのバージョンが増加します。',
  forMoreInformationAboutBuildingSeeThe: 'ビルドに関する詳細は、次を参照してください',
  taskTemplateReference: 'タスクテンプレートのリファレンス',
  definesWhatArtifactShouldBeDeployedWhenTheTaskRun: 'タスクが実行されるときにデプロイされるべきアーティファクトを定義します。',
  forMoreInformationAboutDeployingSeeThe: 'デプロイに関する詳細は、次を参照してください',
  taskTemplateReference2: 'タスクテンプレートのリファレンス',
  definesAutorunSchedule: '自動実行スケジュールを定義します。',
  forMoreInformationAboutCronSeeThe: 'Cronに関する詳細は、次を参照してください',
  cronExpressionFormatReference: 'Cron式フォーマットリファレンス',
  startVersion: '開始バージョン',
  example000: '例: 0.0.0',
  buildTemplate: 'ビルドテンプレート',
  autorun: '自動実行',
  playbookFilename: 'プレイブックファイルへのパス *',
  exampleSiteyml: '例: deploy/site.yml',
  inventory2: 'インベントリ *',
  repository: 'リポジトリ',
  environment3: '変数グループ *',
  vaultPassword: 'ボールトパスワード',
  vaultPassword2: 'ボールトパスワード',
  view: 'ビュー',
  cron: 'Cron',
  iWantToRunATaskByTheCronOnlyForForNewCommitsOfSome: '新しいコミットのためだけにCronでタスクを実行したい',
  repository2: 'リポジトリ',
  cronChecksNewCommitBeforeRun: 'Cronは実行前に新しいコミットをチェックします',
  readThe: '読む',
  toLearnMoreAboutCron: 'Cronについて詳しく学ぶために。',
  suppressSuccessAlerts: '成功アラートを抑制',
  cliArgsJsonArrayExampleIMyinventoryshPrivatekeythe2: 'CLI引数（JSON配列）。例: [ "-i", "@myinventory.sh", "--private-key=/there/id_rsa", "-vvvv" ]',
  allowCliArgsInTask: 'CLI引数',
  docs: 'ドキュメント',
  editViews: 'ビューを編集',
  newTemplate: '新しいテンプレート',
  taskTemplates2: 'タスクテンプレート',
  all: 'すべて',
  notLaunched: '未起動',
  by: '{user_name}による',
  editTemplate: 'テンプレートを編集',
  newTemplate2: '新しいテンプレート',
  deleteTemplate: 'テンプレートを削除',
  playbook: 'プレイブック',
  email: 'メール',
  adminUser: '管理者ユーザー',
  sendAlerts: 'アラートを送信',
  deleteUser: 'ユーザーを削除',
  newUser: '新しいユーザー',
  re: 'Re{getActionButtonTitle}',
  teamMember: '{expr} チームメンバー',
  taskId: 'タスクID',
  version: 'バージョン',
  status: 'ステータス',
  start: '開始',
  actions: 'アクション',
  alert: 'アラート',
  admin: '管理者',
  role: '役割',
  external: '外部',
  time: '時間',
  path: 'パス',
  gitUrl: 'Git URL',
  sshKey: 'SSHキー',
  lastTask: '最後のタスク',
  task2: 'タスク',
  build: 'ビルド',
  deploy: 'デプロイ',
  run: '実行',
  add: '追加',
  password_required: 'パスワードは必須です',
  name_required: '名前は必須です',
  user_credentials_required: 'ユーザー資格情報は必須です',
  type_required: 'タイプは必須です',
  path_required: 'インベントリファイルへのパスは必須です',
  private_key_required: '秘密鍵は必須です',
  project_name_required: 'プロジェクト名は必須です',
  repository_required: 'リポジトリは必須です',
  branch_required: 'ブランチは必須です',
  key_required: 'キーは必須です',
  user_required: 'ユーザーは必須です',
  build_version_required: 'ビルドバージョンは必須です',
  title_required: 'タイトルは必須です',
  isRequired: 'は必須です',
  mustBeInteger: '整数でなければなりません',
  mustBe0OrGreater: '0以上でなければなりません',
  start_version_required: '開始バージョンは必須です',
  playbook_filename_required: 'プレイブックファイル名は必須です',
  inventory_required: 'インベントリは必須です',
  environment_required: '環境は必須です',
  email_required: 'メールは必須です',
  build_template_required: 'ビルドテンプレートは必須です',
  Task: 'タスク',
  Build: 'ビルド',
  Deploy: 'デプロイ',
  Run: '実行',
  ReBuild: '再ビルド',
  ReDeploy: '再デプロイ',
  ReRun: '再実行',
  CreateDemoProject: 'デモプロジェクトを作成',
  LeaveProject: 'プロジェクトを離れる',
  integration: '統合',
  integrations: '統合',
  NewIntegration: '新しい統合',
  EditIntegration: '統合を編集',
  DeleteIntegration: '統合を削除',
  DeleteIntegrationMsg: '本当にこの統合を削除しますか？',
  AddAlias: 'エイリアスを追加',
  LoadAlias: 'エイリアスを読み込み中...',
  globalAlias: 'プロジェクトエイリアスを使用',
  matcher: 'マッチャー',
  matchType: 'マッチタイプ',
  newMatcher: '新しいマッチャー',
  matchMethod: '比較方法',
  matchBodyDataType: 'ボディデータタイプ',
  extractValue: '値を抽出',
  newExtractedValue: '新しい抽出値',
  extractedValueSource: '値のソース',
  matchKey: 'キー',
  matchValue: '値',
  matchOn: 'マッチする',
  runners: 'ランナー',
  newRunner: '新しいランナー',
  enabled: '有効',
  scheduleNextRun: '次の実行',
  maxNumberOfParallelTasks: '最大並列タスク数',
  runnerUsage: '使用法:',
  runnerDockerCommand: 'Docker:',
  runnerToken: 'トークン:',
  editRunner: 'ランナーを編集',
  deleteRunner: 'ランナーを削除',
  newRunnerToken: '新しいランナー',
  askDeleteRunner: '本当にランナー {runner} を削除しますか？',
  project_runners_only_pro: 'プロジェクトレベルのランナーは<b>PRO</b>バージョンでのみ利用可能です。',
  foss_runners_limited: 'オープンソース版は機能が制限されています。完全な機能は<b>PRO</b>バージョンにあります。',
  learn_more_about_pro: '詳細を学ぶ',

  projectRestoreResult: 'プロジェクト復元結果',
  projectWithNameRestored: 'プロジェクト {projectName} が正常に復元されました。',
  pleaseUpdateAccessKeys: 'タスクを実行する前にそれらを更新してください。',
  emptyKeysRestored: '{emptyKeys} の空のキーが追加されました。',
  template: 'テンプレート',
  aliasUrlCopied: 'エイリアスURLがクリップボードにコピーされました。',
  yes: 'はい',
  activeTasks: 'アクティブタスク',
  taskLocation: '場所',
  empty: '空',
  noValues: '値がありません',
  addArg: '引数を追加',

  status_success: '成功',
  status_failed: '失敗',
  status_stopped: '停止',

  api_tokens: 'APIトークン',

  // Terraform/OpenTofu
  auto_approve: '自動承認',

  // Ansible
  tag: 'タグ',
  tag_required: 'タグは必須です',
  allowInventoryInTask: 'インベントリ',
  allowLimitInTask: '制限',
  addLimit: '制限を追加',
  allowDebug: 'デバッグ',
  addTag: 'タグを追加',
  skipTags: 'スキップタグ',
  addSkippedTag: 'スキップされたタグを追加',
  tags: 'タグ',
  limit: '制限',

  runner_tag: 'ランナータグ',
  task_prompts: 'プロンプト',
  template_advanced: '高度なオプション',
  template_app_options: '{app}オプション',
  template_app_prompts: '{app}プロンプト',
  general_settings: '一般',
  danger_zone_settings: '危険ゾーン',
  project_stats: '統計',
  allow_override_branch: 'ブランチ',
  template_common_options: '共通オプション',
};
