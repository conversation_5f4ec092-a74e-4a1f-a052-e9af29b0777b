export default {
  checkInterval: '檢查間隔',
  newCommitCheckInterval: '新提交檢查間隔',
  schedule: '排程',
  newSchedule: '新排程',
  deleteSchedule: '刪除排程',
  editSchedule: '編輯排程',
  backup: '備份專案',
  downloadTheProjectBackupFile: '下載專案備份檔案。',
  restoreProject: '還原專案...',
  incorrectUsrPwd: '登入或密碼不正確',
  askDeleteUser: '您確定要刪除這個使用者嗎？',
  askDeleteTemp: '您確定要刪除這個範本嗎？',
  askDeleteEnv: '您確定要刪除這個環境嗎？',
  askDeleteInv: '您確定要刪除這個庫存嗎？',
  askDeleteKey: '您確定要刪除這個金鑰嗎？',
  askDeleteRepo: '您確定要刪除這個儲存庫嗎？',
  askDeleteProj: '您確定要刪除這個專案嗎？',
  askDeleteTMem: '您確定要刪除這個團隊成員嗎？',
  askDeleteSchedule: '您確定要刪除這個排程嗎？',
  edit: '編輯',
  nnew: '新增',
  keyFormSshKey: 'SSH 金鑰',
  keyFormLoginPassword: '使用密碼登入',
  keyFormNone: '無',
  incorrectUrl: '不正確的 URL',
  username: '使用者名稱',
  username_required: '使用者名稱是必填的',
  dashboard: '儀表板',
  history: '歷史',
  activity: '活動',
  settings: '設定',
  signIn: '登入',
  password: '密碼',
  changePassword: '更改密碼',
  editUser: '編輯使用者',
  newProject: '新專案',
  close: '關閉',
  newProject2: '新專案...',
  demoMode: '示範模式',
  task: '任務 #{expr}',
  youCanRunAnyTasks: '您可以執行任何任務',
  youHaveReadonlyAccess: '您擁有唯讀訪問權限',
  taskTemplates: '任務範本',
  inventory: '庫存',
  environment: '變數群組',
  keyStore: '金鑰儲存',
  repositories: '儲存庫',
  darkMode: '黑暗模式',
  team: '團隊',
  users: '使用者',
  editAccount: '編輯帳戶',
  signOut: '登出',
  error: '錯誤',
  refreshPage: '刷新頁面',
  relogin: '重新登入',
  howToFixSigninIssues: '如何修復登入問題',
  firstlyYouNeedAccessToTheServerWhereSemaphoreRunni: '首先，您需要訪問運行 Semaphore 的伺服器。',
  executeTheFollowingCommandOnTheServerToSeeExisting: '在伺服器上執行以下命令以查看現有使用者：',
  semaphoreUserList: 'semaphore user list',
  youCanChangePasswordOfExistingUser: '您可以更改現有使用者的密碼：',
  semaphoreUserChangebyloginLoginUser123Password: 'semaphore user change-by-login --login user123 --password {makePasswordExample}',
  orCreateNewAdminUser: '或創建新的管理員使用者：',
  close2: '關閉',
  semaphore: '信號燈',
  dontHaveAccountOrCantSignIn: '沒有帳戶或無法登入？',
  password2: '密碼',
  cancel: '取消',
  noViews: '沒有視圖',
  addView: '新增視圖',
  editEnvironment: '編輯變數群組',
  deleteEnvironment: '刪除變數群組',
  newEnvironment: '新群組',
  environmentName: '群組名稱',
  extraVariables: '額外變數',
  enterExtraVariablesJson: '輸入額外變數 JSON...',
  environmentVariables: '環境變數',
  enterEnvJson: '輸入環境 JSON...',
  environmentAndExtraVariablesMustBeValidJsonExample: '環境和額外變數必須是有效的 JSON。範例：',
  dashboard2: '儀表板',
  ansibleSemaphore: 'Semaphore UI',
  wereSorryButHtmlwebpackpluginoptionstitleDoesntWor: '我們很抱歉，但 <%= htmlWebpackPlugin.options.title %> 在未啟用 JavaScript 的情況下無法正常工作。請啟用它以繼續。',
  deleteInventory: '刪除庫存',
  newInventory: '新庫存',
  name: '名稱',
  userCredentials: '使用者憑證',
  sudoCredentialsOptional: 'Sudo 憑證（可選）',
  type: '類型',
  pathToInventoryFile: '庫存檔案路徑',
  enterInventory: '輸入庫存...',
  staticInventoryExample: '靜態庫存範例：',
  staticYamlInventoryExample: '靜態 YAML 庫存範例：',
  keyName: '金鑰名稱',
  loginOptional: '登入（可選）',
  usernameOptional: '使用者名稱（可選）',
  privateKey: '私鑰',
  override: '覆蓋',
  useThisTypeOfKeyForHttpsRepositoriesAndForPlaybook: '對於 HTTPS 儲存庫和使用非 SSH 連接的劇本，請使用這種類型的金鑰。',
  deleteKey: '刪除金鑰',
  newKey: '新金鑰',
  create: '創建',
  newTask: '新任務',
  cantDeleteThe: '無法刪除 {objectTitle}',
  theCantBeDeletedBecauseItUsedByTheResourcesBelow: '{objectTitle} 無法刪除，因為它被以下資源使用',
  projectName: '專案名稱',
  allowAlertsForThisProject: '允許此專案的警報',
  telegramChatIdOptional: 'Telegram 聊天 ID（可選）',
  maxNumberOfParallelTasksOptional: '最大並行任務數（可選）',
  deleteRepository: '刪除儲存庫',
  newRepository: '新儲存庫',
  urlOrPath: 'URL 或路徑',
  absPath: '絕對路徑',
  branch: '分支',
  accessKey: '訪問金鑰',
  credentialsToAccessToTheGitRepositoryItShouldBe: '訪問 Git 儲存庫的憑證。應該是：',
  ifYouUseGitOrSshUrl: '如果您使用 Git 或 SSH URL。',
  ifYouUseHttpsOrFileUrl: '如果您使用 HTTPS 或檔案 URL。',
  none: '無',
  ssh: 'SSH',
  deleteProject: '刪除專案',
  save: '保存',
  deleteProject2: '刪除專案',
  onceYouDeleteAProjectThereIsNoGoingBackPleaseBeCer: '一旦您刪除專案，就無法恢復。請確定。',

  clear_cache: '清除快取',
  clear_cache_message: '刪除與此專案相關的所有快取檔案。此操作不可逆。',

  name2: '名稱 *',
  title: '標題 *',
  description: '描述',
  required: '必填',
  key: '{expr}',
  surveyVariables: '調查變數',
  addVariable: '新增變數',
  vaultName: '保險庫 ID（可選）',
  vaultNameDefault: '只能存在一個 `default`（空）名稱',
  vaultNameUnique: '必須是唯一的',
  vaultTypePassword: '密碼',
  vaultTypeScript: '客戶端腳本',
  vaultScript: '腳本路徑',
  vaultScriptRequired: '腳本路徑是必填的',
  vaultScriptClientRequired: '腳本路徑必須以 \' -client \' 和擴展名結尾',
  vaults: '保險庫',
  vaultAdd: '新增保險庫',
  vaultRequired: '保險庫密碼是必填的',
  columns: '欄位',
  buildVersion: '建置版本',
  messageOptional: '訊息（可選）',
  debug: '除錯',
  dryRun: '模擬執行',
  diff: '差異',
  advanced: '進階',
  hide: '隱藏',
  pleaseAllowOverridingCliArgumentInTaskTemplateSett: '要允許在任務範本設定中覆蓋 CLI 參數',
  cliArgsJsonArrayExampleIMyinventoryshPrivatekeythe: 'CLI 參數（JSON 陣列）。範例： [ "-i", "@myinventory.sh", "--private-key=/there/id_rsa", "-vvvv" ]',
  started: '已開始',
  author: '作者',
  duration: '持續時間',
  stop: '停止',
  forceStop: '強制停止',
  raw_log: '原始日誌',

  confirmTask: '確認',
  deleteTeamMember: '刪除團隊成員',
  team2: '團隊',
  newTeamMember: '新團隊成員',
  user: '使用者',
  administrator: '管理員',
  definesStartVersionOfYourArtifactEachRunIncrements: '定義您的工件的起始版本。每次運行都會增加工件版本。',
  forMoreInformationAboutBuildingSeeThe: '有關建置的更多資訊，請參見',
  taskTemplateReference: '任務範本參考',
  definesWhatArtifactShouldBeDeployedWhenTheTaskRun: '定義在任務運行時應部署的工件。',
  forMoreInformationAboutDeployingSeeThe: '有關部署的更多資訊，請參見',
  taskTemplateReference2: '任務範本參考',
  definesAutorunSchedule: '定義自動運行排程。',
  forMoreInformationAboutCronSeeThe: '有關 cron 的更多資訊，請參見',
  cronExpressionFormatReference: 'Cron 表達式格式參考',
  startVersion: '起始版本',
  example000: '範例： 0.0.0',
  buildTemplate: '建置範本',
  autorun: '自動運行',
  playbookFilename: '劇本檔案路徑 *',
  exampleSiteyml: '範例： deploy/site.yml',
  inventory2: '庫存 *',
  repository: '儲存庫',
  environment3: '變數群組 *',
  vaultPassword: '保險庫密碼',
  vaultPassword2: '保險庫密碼',
  view: '視圖',
  cron: 'Cron',
  iWantToRunATaskByTheCronOnlyForForNewCommitsOfSome: '我想通過 cron 僅為某些儲存庫的新提交運行任務',
  repository2: '儲存庫',
  cronChecksNewCommitBeforeRun: 'Cron 在運行之前檢查新提交',
  readThe: '閱讀',
  toLearnMoreAboutCron: '以了解有關 Cron 的更多資訊。',
  suppressSuccessAlerts: '抑制成功警報',
  cliArgsJsonArrayExampleIMyinventoryshPrivatekeythe2: 'CLI 參數（JSON 陣列）。範例： [ "-i", "@myinventory.sh", "--private-key=/there/id_rsa", "-vvvv" ]',
  allowCliArgsInTask: 'CLI 參數',
  docs: '文檔',
  editViews: '編輯視圖',
  newTemplate: '新範本',
  taskTemplates2: '任務範本',
  all: '所有',
  notLaunched: '未啟動',
  by: '由 {user_name}',
  editTemplate: '編輯範本',
  newTemplate2: '新範本',
  deleteTemplate: '刪除範本',
  playbook: '劇本',
  email: '電子郵件',
  adminUser: '管理員使用者',
  sendAlerts: '發送警報',
  deleteUser: '刪除使用者',
  newUser: '新使用者',
  re: '重新 {getActionButtonTitle}',
  teamMember: '{expr} 團隊成員',
  taskId: '任務 ID',
  version: '版本',
  status: '狀態',
  start: '開始',
  actions: '操作',
  alert: '警報',
  admin: '管理員',
  role: '角色',
  external: '外部',
  time: '時間',
  path: '路徑',
  gitUrl: 'Git URL',
  sshKey: 'SSH 金鑰',
  lastTask: '最後任務',
  task2: '任務',
  build: '建置',
  deploy: '部署',
  run: '運行',
  add: '新增',
  password_required: '密碼是必填的',
  name_required: '名稱是必填的',
  user_credentials_required: '使用者憑證是必填的',
  type_required: '類型是必填的',
  path_required: '庫存檔案路徑是必填的',
  private_key_required: '私鑰是必填的',
  project_name_required: '專案名稱是必填的',
  repository_required: '儲存庫是必填的',
  branch_required: '分支是必填的',
  key_required: '金鑰是必填的',
  user_required: '使用者是必填的',
  build_version_required: '建置版本是必填的',
  title_required: '標題是必填的',
  isRequired: '是必填的',
  mustBeInteger: '必須是整數',
  mustBe0OrGreater: '必須是 0 或更大',
  start_version_required: '起始版本是必填的',
  playbook_filename_required: '劇本檔案名稱是必填的',
  inventory_required: '庫存是必填的',
  environment_required: '環境是必填的',
  email_required: '電子郵件是必填的',
  build_template_required: '建置範本是必填的',
  Task: '任務',
  Build: '建置',
  Deploy: '部署',
  Run: '運行',
  ReBuild: '重建',
  ReDeploy: '重新部署',
  ReRun: '重新運行',
  CreateDemoProject: '創建示範專案',
  LeaveProject: '離開專案',
  integration: '整合',
  integrations: '整合',
  NewIntegration: '新整合',
  EditIntegration: '編輯整合',
  DeleteIntegration: '刪除整合',
  DeleteIntegrationMsg: '您確定要刪除這個整合嗎？',
  AddAlias: '新增別名',
  LoadAlias: '正在加載別名...',
  globalAlias: '使用專案別名',
  matcher: '匹配器',
  matchType: '匹配類型',
  newMatcher: '新匹配器',
  matchMethod: '比較方法',
  matchBodyDataType: '主體數據類型',
  extractValue: '提取值',
  newExtractedValue: '新提取值',
  extractedValueSource: '值來源',
  matchKey: '鍵',
  matchValue: '值',
  matchOn: '匹配於',
  runners: '執行者',
  newRunner: '新執行者',
  enabled: '已啟用',
  scheduleNextRun: '下一次運行',
  maxNumberOfParallelTasks: '最大並行任務數',
  runnerUsage: '使用：',
  runnerDockerCommand: 'Docker：',
  runnerToken: '令牌：',
  editRunner: '編輯執行者',
  deleteRunner: '刪除執行者',
  newRunnerToken: '新執行者',
  askDeleteRunner: '您確定要刪除執行者 {runner} 嗎？',
  project_runners_only_pro: '專案級執行者僅在 <b>PRO</b> 版本中可用。',
  foss_runners_limited: '開源版本功能有限；完整功能在 <b>PRO</b> 版本中。',
  learn_more_about_pro: '了解更多',

  projectRestoreResult: '專案還原結果',
  projectWithNameRestored: '專案 {projectName} 成功還原。',
  pleaseUpdateAccessKeys: '請在運行任務之前更新它們。',
  emptyKeysRestored: '{emptyKeys} 個空金鑰已添加。',
  template: '範本',
  aliasUrlCopied: '別名 URL 已複製到剪貼簿。',
  yes: '是',
  activeTasks: '活動任務',
  taskLocation: '位置',
  empty: '空',
  noValues: '沒有值',
  addArg: '新增參數',

  status_success: '成功',
  status_failed: '失敗',
  status_stopped: '已停止',

  api_tokens: 'API 令牌',

  // Terraform/OpenTofu
  auto_approve: '自動批准',

  // Ansible
  tag: '標籤',
  tag_required: '標籤是必填的',
  allowInventoryInTask: '庫存',
  allowLimitInTask: '限制',
  addLimit: '新增限制',
  allowDebug: '除錯',
  addTag: '新增標籤',
  skipTags: '跳過標籤',
  addSkippedTag: '新增跳過的標籤',
  tags: '標籤',
  limit: '限制',

  runner_tag: '執行者標籤',
  task_prompts: '提示',
  template_advanced: '進階選項',
  template_app_options: '{app} 選項',
  template_app_prompts: '{app} 提示',
  general_settings: '一般',
  danger_zone_settings: '危險區域',
  project_stats: '統計',
  allow_override_branch: '分支',
  template_common_options: '通用選項',
};
