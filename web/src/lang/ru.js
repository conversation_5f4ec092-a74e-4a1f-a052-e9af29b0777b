export default {
  checkInterval: 'интервалПроверки',
  newCommitCheckInterval: 'Интервал проверки новых коммитов',
  schedule: 'Расписание',
  newSchedule: 'Новое расписание',
  deleteSchedule: 'Удалить расписание',
  editSchedule: 'Редактировать расписание',
  backup: 'Резервное копирование проекта',
  downloadTheProjectBackupFile: 'Скачать файл резервной копии проекта.',
  restoreProject: 'Восстановить проект...',
  incorrectUsrPwd: 'Неверный логин или пароль',
  askDeleteUser: 'Вы действительно хотите удалить этого пользователя?',
  askDeleteTemp: 'Вы действительно хотите удалить этот шаблон?',
  askDeleteEnv: 'Вы действительно хотите удалить эту среду?',
  askDeleteInv: 'Вы действительно хотите удалить этот инвентарь?',
  askDeleteKey: 'Вы действительно хотите удалить этот ключ?',
  askDeleteRepo: 'Вы действительно хотите удалить этот репозиторий?',
  askDeleteProj: 'Вы действительно хотите удалить этот проект?',
  askDeleteTMem: 'Вы действительно хотите удалить этого участника команды?',
  askDeleteSchedule: 'Вы действительно хотите удалить это расписание?',
  edit: 'Редактировать',
  nnew: 'Новый',
  keyFormSshKey: 'SSH ключ',
  keyFormLoginPassword: 'Логин с паролем',
  keyFormNone: 'Нет',
  incorrectUrl: 'Неверный URL',
  username: 'Имя пользователя',
  username_required: 'Имя пользователя обязательно',
  dashboard: 'Панель управления',
  history: 'История',
  activity: 'Активность',
  settings: 'Настройки',
  signIn: 'Войти',
  password: 'Пароль',
  changePassword: 'Сменить пароль',
  editUser: 'Редактировать пользователя',
  newProject: 'Новый проект',
  close: 'Закрыть',
  newProject2: 'Новый проект...',
  demoMode: 'РЕЖИМ ДЕМО',
  task: 'Задача #{expr}',
  youCanRunAnyTasks: 'Вы можете запускать любые задачи',
  youHaveReadonlyAccess: 'У вас доступ только для чтения',
  taskTemplates: 'Шаблоны задач',
  inventory: 'Инвентарь',
  environment: 'Группы переменных',
  keyStore: 'Хранилище ключей',
  repositories: 'Репозитории',
  darkMode: 'Темный режим',
  team: 'Команда',
  users: 'Пользователи',
  editAccount: 'Редактировать аккаунт',
  signOut: 'Выйти',
  error: 'Ошибка',
  refreshPage: 'Обновить страницу',
  relogin: 'Повторный вход',
  howToFixSigninIssues: 'Как исправить проблемы с входом',
  firstlyYouNeedAccessToTheServerWhereSemaphoreRunni: 'Во-первых, вам нужен доступ к серверу, на котором работает Semaphore.',
  executeTheFollowingCommandOnTheServerToSeeExisting: 'Выполните следующую команду на сервере, чтобы увидеть существующих пользователей:',
  semaphoreUserList: 'semaphore user list',
  youCanChangePasswordOfExistingUser: 'Вы можете изменить пароль существующего пользователя:',
  semaphoreUserChangebyloginLoginUser123Password: 'semaphore user change-by-login --login user123 --password {makePasswordExample}',
  orCreateNewAdminUser: 'Или создайте нового администратора:',
  close2: 'Закрыть',
  semaphore: 'СИГНАЛ',
  dontHaveAccountOrCantSignIn: 'Нет аккаунта или не можете войти?',
  password2: 'Пароль',
  cancel: 'Отмена',
  noViews: 'Нет представлений',
  addView: 'Добавить представление',
  editEnvironment: 'Редактировать группу переменных',
  deleteEnvironment: 'Удалить группу переменных',
  newEnvironment: 'Новая группа',
  environmentName: 'Имя группы',
  extraVariables: 'Дополнительные переменные',
  enterExtraVariablesJson: 'Введите дополнительные переменные в формате JSON...',
  environmentVariables: 'Переменные среды',
  enterEnvJson: 'Введите env JSON...',
  environmentAndExtraVariablesMustBeValidJsonExample: 'Переменные среды и дополнительные переменные должны быть действительным JSON. Пример:',
  dashboard2: 'Панель управления',
  ansibleSemaphore: 'Интерфейс Semaphore',
  wereSorryButHtmlwebpackpluginoptionstitleDoesntWor: 'Извините, но <%= htmlWebpackPlugin.options.title %> не работает должным образом без включенного JavaScript. Пожалуйста, включите его, чтобы продолжить.',
  deleteInventory: 'Удалить инвентарь',
  newInventory: 'Новый инвентарь',
  name: 'Имя',
  userCredentials: 'Учетные данные пользователя',
  sudoCredentialsOptional: 'Учетные данные sudo (необязательно)',
  type: 'Тип',
  pathToInventoryFile: 'Путь к файлу инвентаря',
  enterInventory: 'Введите инвентарь...',
  staticInventoryExample: 'Пример статического инвентаря:',
  staticYamlInventoryExample: 'Пример статического YAML инвентаря:',
  keyName: 'Имя ключа',
  loginOptional: 'Логин (необязательно)',
  usernameOptional: 'Имя пользователя (необязательно)',
  privateKey: 'Закрытый ключ',
  override: 'Переопределить',
  useThisTypeOfKeyForHttpsRepositoriesAndForPlaybook: 'Используйте этот тип ключа для HTTPS репозиториев и для плейбуков, которые используют не SSH соединения.',
  deleteKey: 'Удалить ключ',
  newKey: 'Новый ключ',
  create: 'Создать',
  newTask: 'Новая задача',
  cantDeleteThe: 'Невозможно удалить {objectTitle}',
  theCantBeDeletedBecauseItUsedByTheResourcesBelow: '{objectTitle} не может быть удален, так как он используется ресурсами ниже',
  projectName: 'Имя проекта',
  allowAlertsForThisProject: 'Разрешить оповещения для этого проекта',
  telegramChatIdOptional: 'Telegram Chat ID (необязательно)',
  maxNumberOfParallelTasksOptional: 'Максимальное количество параллельных задач (необязательно)',
  deleteRepository: 'Удалить репозиторий',
  newRepository: 'Новый репозиторий',
  urlOrPath: 'URL или путь',
  absPath: 'абс. путь',
  branch: 'Ветка',
  accessKey: 'Ключ доступа',
  credentialsToAccessToTheGitRepositoryItShouldBe: 'Учетные данные для доступа к Git репозиторию. Это должно быть:',
  ifYouUseGitOrSshUrl: 'если вы используете Git или SSH URL.',
  ifYouUseHttpsOrFileUrl: 'если вы используете HTTPS или file URL.',
  none: 'Нет',
  ssh: 'SSH',
  deleteProject: 'Удалить проект',
  save: 'Сохранить',
  deleteProject2: 'Удалить проект',
  onceYouDeleteAProjectThereIsNoGoingBackPleaseBeCer: 'После удаления проекта нет возврата. Пожалуйста, будьте уверены.',

  clear_cache: 'Очистить кэш',
  clear_cache_message: 'Удалить все файлы кэша, связанные с этим проектом. Это действие необратимо.',

  name2: 'Имя *',
  title: 'Название *',
  description: 'Описание',
  required: 'Обязательно',
  key: '{expr}',
  surveyVariables: 'Переменные опроса',
  addVariable: 'Добавить переменную',
  vaultName: 'ID хранилища (необязательно)',
  vaultNameDefault: 'Может существовать только одно имя `default` (пустое)',
  vaultNameUnique: 'Должно быть уникальным',
  vaultTypePassword: 'Пароль',
  vaultTypeScript: 'Клиентский скрипт',
  vaultScript: 'Путь к скрипту',
  vaultScriptRequired: 'Путь к скрипту обязателен',
  vaultScriptClientRequired: 'Путь к скрипту должен заканчиваться на \'-client\' и иметь расширение',
  vaults: 'Хранилища',
  vaultAdd: 'Добавить хранилище',
  vaultRequired: 'Пароль хранилища обязателен',
  columns: 'Столбцы',
  buildVersion: 'Версия сборки',
  messageOptional: 'Сообщение (необязательно)',
  debug: 'Отладка',
  dryRun: 'Пробный запуск',
  diff: 'Различия',
  advanced: 'Расширенные',
  hide: 'Скрыть',
  pleaseAllowOverridingCliArgumentInTaskTemplateSett: 'Чтобы разрешить переопределение аргумента CLI в настройках шаблона задачи',
  cliArgsJsonArrayExampleIMyinventoryshPrivatekeythe: 'CLI аргументы (JSON массив). Пример: [ "-i", "@myinventory.sh", "--private-key=/there/id_rsa", "-vvvv" ]',
  started: 'Запущено',
  author: 'Автор',
  duration: 'Продолжительность',
  stop: 'Остановить',
  forceStop: 'Принудительная остановка',
  raw_log: 'Сырой лог',

  confirmTask: 'Подтвердить',
  deleteTeamMember: 'Удалить участника команды',
  team2: 'Команда',
  newTeamMember: 'Новый участник команды',
  user: 'Пользователь',
  administrator: 'Администратор',
  definesStartVersionOfYourArtifactEachRunIncrements: 'Определяет начальную версию вашего артефакта. Каждое выполнение увеличивает версию артефакта.',
  forMoreInformationAboutBuildingSeeThe: 'Для получения дополнительной информации о сборке смотрите',
  taskTemplateReference: 'Справка по шаблону задачи',
  definesWhatArtifactShouldBeDeployedWhenTheTaskRun: 'Определяет, какой артефакт должен быть развернут при выполнении задачи.',
  forMoreInformationAboutDeployingSeeThe: 'Для получения дополнительной информации о развертывании смотрите',
  taskTemplateReference2: 'Справка по шаблону задачи',
  definesAutorunSchedule: 'Определяет расписание автозапуска.',
  forMoreInformationAboutCronSeeThe: 'Для получения дополнительной информации о cron смотрите',
  cronExpressionFormatReference: 'Справка по формату выражений cron',
  startVersion: 'Начальная версия',
  example000: 'Пример: 0.0.0',
  buildTemplate: 'Шаблон сборки',
  autorun: 'Автозапуск',
  playbookFilename: 'Путь к файлу плейбука *',
  exampleSiteyml: 'Пример: deploy/site.yml',
  inventory2: 'Инвентарь *',
  repository: 'Репозиторий',
  environment3: 'Группа переменных *',
  vaultPassword: 'Пароль хранилища',
  vaultPassword2: 'Пароль хранилища',
  view: 'Представление',
  cron: 'Cron',
  iWantToRunATaskByTheCronOnlyForForNewCommitsOfSome: 'Я хочу запускать задачу по cron только для новых коммитов некоторого репозитория',
  repository2: 'Репозиторий',
  cronChecksNewCommitBeforeRun: 'Cron проверяет новый коммит перед запуском',
  readThe: 'Читать',
  toLearnMoreAboutCron: 'чтобы узнать больше о Cron.',
  suppressSuccessAlerts: 'Подавить успешные оповещения',
  cliArgsJsonArrayExampleIMyinventoryshPrivatekeythe2: 'CLI аргументы (JSON массив). Пример: [ "-i", "@myinventory.sh", "--private-key=/there/id_rsa", "-vvvv" ]',
  allowCliArgsInTask: 'CLI аргументы',
  docs: 'документация',
  editViews: 'Редактировать представления',
  newTemplate: 'Новый шаблон',
  taskTemplates2: 'Шаблоны задач',
  all: 'Все',
  notLaunched: 'Не запущено',
  by: 'от {user_name}',
  editTemplate: 'Редактировать шаблон',
  newTemplate2: 'Новый шаблон',
  deleteTemplate: 'Удалить шаблон',
  playbook: 'Плейбук',
  email: 'Электронная почта',
  adminUser: 'Администратор',
  sendAlerts: 'Отправить оповещения',
  deleteUser: 'Удалить пользователя',
  newUser: 'Новый пользователь',
  re: 'Re{getActionButtonTitle}',
  teamMember: '{expr} Участник команды',
  taskId: 'ID задачи',
  version: 'Версия',
  status: 'Статус',
  start: 'Начать',
  actions: 'Действия',
  alert: 'Оповещение',
  admin: 'Админ',
  role: 'Роль',
  external: 'Внешний',
  time: 'Время',
  path: 'Путь',
  gitUrl: 'Git URL',
  sshKey: 'SSH ключ',
  lastTask: 'Последняя задача',
  task2: 'Задача',
  build: 'Сборка',
  deploy: 'Развертывание',
  run: 'Запуск',
  add: 'Добавить',
  password_required: 'Пароль обязателен',
  name_required: 'Имя обязательно',
  user_credentials_required: 'Учетные данные пользователя обязательны',
  type_required: 'Тип обязателен',
  path_required: 'Путь к файлу инвентаря обязателен',
  private_key_required: 'Закрытый ключ обязателен',
  project_name_required: 'Имя проекта обязательно',
  repository_required: 'Репозиторий обязателен',
  branch_required: 'Ветка обязательна',
  key_required: 'Ключ обязателен',
  user_required: 'Пользователь обязателен',
  build_version_required: 'Версия сборки обязательна',
  title_required: 'Название обязательно',
  isRequired: 'обязательно',
  mustBeInteger: 'Должно быть целым числом',
  mustBe0OrGreater: 'Должно быть 0 или больше',
  start_version_required: 'Начальная версия обязательна',
  playbook_filename_required: 'Имя файла плейбука обязательно',
  inventory_required: 'Инвентарь обязателен',
  environment_required: 'Среда обязательна',
  email_required: 'Электронная почта обязательна',
  build_template_required: 'Шаблон сборки обязателен',
  Task: 'Задача',
  Build: 'Сборка',
  Deploy: 'Развертывание',
  Run: 'Запуск',
  ReBuild: 'Пересобрать',
  ReDeploy: 'Переразвернуть',
  ReRun: 'Повторный запуск',
  CreateDemoProject: 'Создать демонстрационный проект',
  LeaveProject: 'Покинуть проект',
  integration: 'Интеграция',
  integrations: 'Интеграции',
  NewIntegration: 'Новая интеграция',
  EditIntegration: 'Редактировать интеграцию',
  DeleteIntegration: 'Удалить интеграцию',
  DeleteIntegrationMsg: 'Вы уверены, что хотите удалить эту интеграцию?',
  AddAlias: 'Добавить псевдоним',
  LoadAlias: 'Загрузка псевдонимов...',
  globalAlias: 'Использовать псевдоним проекта',
  matcher: 'Сравнитель',
  matchType: 'Тип совпадения',
  newMatcher: 'Новый сравнитель',
  matchMethod: 'Метод сравнения',
  matchBodyDataType: 'Тип данных тела',
  extractValue: 'Извлечь значение',
  newExtractedValue: 'Новое извлеченное значение',
  extractedValueSource: 'Источник значения',
  matchKey: 'Ключ',
  matchValue: 'Значение',
  matchOn: 'Совпадение по',
  runners: 'Исполнители',
  newRunner: 'Новый исполнитель',
  enabled: 'Включено',
  scheduleNextRun: 'Следующий запуск',
  maxNumberOfParallelTasks: 'Максимальное количество параллельных задач',
  runnerUsage: 'Использование:',
  runnerDockerCommand: 'Docker:',
  runnerToken: 'Токен:',
  editRunner: 'Редактировать исполнителя',
  deleteRunner: 'Удалить исполнителя',
  newRunnerToken: 'Новый токен исполнителя',
  askDeleteRunner: 'Вы действительно хотите удалить исполнителя {runner}?',
  project_runners_only_pro: 'Исполнители на уровне проекта доступны только в версии <b>PRO</b>.',
  foss_runners_limited: 'Открытая версия имеет ограниченный функционал; полный функционал доступен в версии <b>PRO</b>.',
  learn_more_about_pro: 'Узнать больше',

  projectRestoreResult: 'Результаты восстановления проекта',
  projectWithNameRestored: 'Проект {projectName} успешно восстановлен.',
  pleaseUpdateAccessKeys: 'Пожалуйста, обновите их перед запуском задач.',
  emptyKeysRestored: '{emptyKeys} пустых ключей добавлено.',
  template: 'Шаблон',
  aliasUrlCopied: 'URL псевдонима скопирован в буфер обмена.',
  yes: 'Да',
  activeTasks: 'Активные задачи',
  taskLocation: 'Местоположение',
  empty: 'Пусто',
  noValues: 'Нет значений',
  addArg: 'Добавить аргумент',

  status_success: 'Успех',
  status_failed: 'Неудача',
  status_stopped: 'Остановлено',

  api_tokens: 'API токены',

  // Terraform/OpenTofu
  auto_approve: 'Авто одобрение',

  // Ansible
  tag: 'Тег',
  tag_required: 'Тег обязателен',
  allowInventoryInTask: 'Инвентарь',
  allowLimitInTask: 'Лимит',
  addLimit: 'Добавить лимит',
  allowDebug: 'Отладка',
  addTag: 'Добавить тег',
  skipTags: 'Пропустить теги',
  addSkippedTag: 'Добавить пропущенный тег',
  tags: 'Теги',
  limit: 'Лимит',

  runner_tag: 'Тег исполнителя',
  task_prompts: 'Подсказки',
  template_advanced: 'Расширенные параметры',
  template_app_options: '{app} параметры',
  template_app_prompts: '{app} подсказки',
  general_settings: 'Общие',
  danger_zone_settings: 'Опасная зона',
  project_stats: 'Статистика',
  allow_override_branch: 'Ветка',
  template_common_options: 'Общие параметры',
};
