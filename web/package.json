{"name": "web", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint"}, "dependencies": {"@mdi/font": "^7.0.0", "ansi_up": "^6.0.2", "axios": "^1.8.3", "chart.js": "^3.8.0", "chartjs-adapter-moment": "^1.0.1", "core-js": "^3.40.0", "cron-parser": "^4.9.0", "moment": "^2.29.4", "vue": "^2.6.14", "vue-chartjs": "^4.0.0", "vue-codemirror": "^4.0.6", "vue-i18n": "^8.18.2", "vue-router": "^3.5.4", "vue-virtual-scroll-list": "^2.3.5", "vuedraggable": "^2.24.3", "vuetify": "^2.6.10"}, "devDependencies": {"@vue/cli-plugin-babel": "^5.0.6", "@vue/cli-plugin-eslint": "^5.0.6", "@vue/cli-plugin-router": "^5.0.6", "@vue/cli-plugin-unit-mocha": "^5.0.6", "@vue/cli-service": "^5.0.6", "@vue/eslint-config-airbnb": "^6.0.0", "@vue/test-utils": "^2.0.0", "babel-eslint": "^10.1.0", "chai": "^5.0.0", "dotenv": "^16.4.5", "eslint": "^7.32.0", "eslint-config-prettier": "^10.0.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-vue": "^9.1.1", "eslint-plugin-vuejs-accessibility": "^1.2.0", "glob-parent": ">=5.1.2", "gulp": "^5.0.0", "gulp-cli": "^3.0.0", "gulp-rename": "^2.0.0", "nanoid": ">=3.1.31", "nyc": "^17.0.0", "openai": "^4.65.0", "plugin-error": "^2.0.1", "prettier": "^3.4.2", "sass": "~1.32.12", "sass-loader": "^13.0.0", "stylus": "^0.64.0", "stylus-loader": "^3.0.2", "through2": "^4.0.2", "vue-cli-plugin-vuetify": "~2.5.0", "vue-template-compiler": "^2.6.14", "vuetify-loader": "^1.8.0"}}