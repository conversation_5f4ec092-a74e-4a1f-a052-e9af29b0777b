# Semaphore 专业版功能限制分析

## 🔍 概述

Semaphore 使用 **Go 构建标签 (Build Tags)** 来实现开源版和专业版的功能区分。这是一种编译时的功能控制机制，而不是运行时的许可证验证。

## 🏗 实现原理

### 1. 构建标签机制

Semaphore 使用 Go 的构建标签来编译不同版本：

- **开源版**: 使用 `!pro` 标签编译
- **专业版**: 使用 `pro` 标签编译

### 2. 关键文件分析

#### 后端限制实现

**`api/router.go` (第539-542行)**
```go
"premium_features": map[string]bool{
    "project_runners":   false,  // 项目级运行器
    "terraform_backend": false,  // Terraform 后端
},
```

**`api/projects/runners.go` (带 `!pro` 标签)**
```go
//go:build !pro

func AddRunner(w http.ResponseWriter, r *http.Request) {
    w.WriteHeader(http.StatusNotFound)  // 直接返回404
}

func UpdateRunner(w http.ResponseWriter, r *http.Request) {
    w.WriteHeader(http.StatusNotFound)
}

func DeleteRunner(w http.ResponseWriter, r *http.Request) {
    w.WriteHeader(http.StatusNotFound)
}
```

**数据库层限制**
- `db/bolt/runner.go` (带 `!pro` 标签)
- `db/sql/runner.go` (带 `!pro` 标签)

```go
//go:build !pro

func (d *BoltDb) GetRunner(projectID int, runnerID int) (runner db.Runner, err error) {
    err = db.ErrNotFound  // 直接返回未找到错误
    return
}
```

#### 前端限制实现

**`web/src/views/Runners.vue` (第256-274行)**
```vue
<v-alert
  v-if="!premiumFeatures.project_runners"
  type="info"
  text
  color="hsl(348deg, 86%, 61%)"
>
  <span v-if="projectId" v-html="$t('project_runners_only_pro')"></span>
  <v-btn href="https://semaphoreui.com/pro">
    {{ $t('learn_more_about_pro') }}
  </v-btn>
</v-alert>
```

**`web/src/components/DashboardMenu.vue` (第30-37行)**
```vue
<v-tab
  v-if="projectType === ''"
  key="runners"
  :to="`/project/${projectId}/runners`"
>
  {{ $t('runners') }}
  <v-icon class="ml-1" large color="hsl(348deg, 86%, 61%)">
    mdi-professional-hexagon
  </v-icon>
</v-tab>
```

## 🔓 如何自己实现项目级运行器

### 方法1: 修改构建标签 (最简单)

#### 1. 移除构建标签限制

**修改 `api/projects/runners.go`**
```bash
# 删除第一行的构建标签
sed -i '1d' api/projects/runners.go
```

**修改 `db/bolt/runner.go` 和 `db/sql/runner.go`**
```bash
# 删除构建标签
sed -i '1d' db/bolt/runner.go
sed -i '1d' db/sql/runner.go
```

#### 2. 启用前端功能

**修改 `api/router.go`**
```go
"premium_features": map[string]bool{
    "project_runners":   true,   // 改为 true
    "terraform_backend": false,
},
```

### 方法2: 实现完整的项目级运行器功能

#### 1. 创建新的运行器API文件

**创建 `api/projects/runners_impl.go`**
```go
package projects

import (
    "net/http"
    "github.com/gorilla/context"
    "github.com/semaphoreui/semaphore/api/helpers"
    "github.com/semaphoreui/semaphore/db"
)

func AddRunner(w http.ResponseWriter, r *http.Request) {
    project := context.Get(r, "project").(db.Project)

    var runner db.Runner
    if !helpers.Bind(w, r, &runner) {
        return
    }

    runner.ProjectID = &project.ID

    newRunner, err := helpers.Store(r).CreateRunner(runner)
    if err != nil {
        helpers.WriteError(w, err)
        return
    }

    helpers.WriteJSON(w, http.StatusCreated, newRunner)
}

func UpdateRunner(w http.ResponseWriter, r *http.Request) {
    project := context.Get(r, "project").(db.Project)
    runnerID, err := helpers.GetIntParam("runner_id", w, r)
    if err != nil {
        return
    }

    var runner db.Runner
    if !helpers.Bind(w, r, &runner) {
        return
    }

    runner.ID = runnerID
    runner.ProjectID = &project.ID

    err = helpers.Store(r).UpdateRunner(runner)
    if err != nil {
        helpers.WriteError(w, err)
        return
    }

    w.WriteHeader(http.StatusNoContent)
}

func DeleteRunner(w http.ResponseWriter, r *http.Request) {
    project := context.Get(r, "project").(db.Project)
    runnerID, err := helpers.GetIntParam("runner_id", w, r)
    if err != nil {
        return
    }

    err = helpers.Store(r).DeleteRunner(project.ID, runnerID)
    if err != nil {
        helpers.WriteError(w, err)
        return
    }

    w.WriteHeader(http.StatusNoContent)
}
```

#### 2. 实现数据库层功能

**修改 `db/bolt/runner.go`**
```go
package bolt

import (
    "github.com/semaphoreui/semaphore/db"
)

func (d *BoltDb) GetRunner(projectID int, runnerID int) (runner db.Runner, err error) {
    err = d.getObject(projectID, db.RunnerProps, intObjectID(runnerID), &runner)
    return
}

func (d *BoltDb) GetRunners(projectID int, activeOnly bool, tag *string) (runners []db.Runner, err error) {
    err = d.getObjects(projectID, db.RunnerProps, db.RetrieveQueryParams{}, nil, &runners)

    if activeOnly {
        var activeRunners []db.Runner
        for _, runner := range runners {
            if runner.Active {
                activeRunners = append(activeRunners, runner)
            }
        }
        runners = activeRunners
    }

    if tag != nil {
        var taggedRunners []db.Runner
        for _, runner := range runners {
            if runner.Tag == *tag {
                taggedRunners = append(taggedRunners, runner)
            }
        }
        runners = taggedRunners
    }

    return
}

func (d *BoltDb) DeleteRunner(projectID int, runnerID int) (err error) {
    return d.deleteObject(projectID, db.RunnerProps, intObjectID(runnerID))
}
```

#### 3. 更新路由配置

**修改 `api/router.go`** 中的项目路由部分，确保运行器路由被正确注册。

### 方法3: 使用环境变量控制 (推荐)

#### 1. 添加环境变量控制

**修改 `util/config.go`**
```go
type ConfigType struct {
    // ... 其他配置
    EnableProjectRunners bool `json:"enable_project_runners,omitempty" env:"SEMAPHORE_ENABLE_PROJECT_RUNNERS"`
}
```

**修改 `api/router.go`**
```go
"premium_features": map[string]bool{
    "project_runners":   util.Config.EnableProjectRunners,
    "terraform_backend": false,
},
```

#### 2. 启动时设置环境变量

```bash
export SEMAPHORE_ENABLE_PROJECT_RUNNERS=true
./bin/semaphore server --config config.json
```

## 🛠 实施步骤

### 快速启用 (5分钟)

1. **修改后端功能标志**
   ```bash
   # 编辑 api/router.go，将 project_runners 改为 true
   sed -i 's/"project_runners":   false/"project_runners":   true/' api/router.go
   ```

2. **移除API限制**
   ```bash
   # 删除构建标签
   sed -i '1d' api/projects/runners.go
   sed -i '1d' db/bolt/runner.go
   sed -i '1d' db/sql/runner.go
   ```

3. **重新编译和启动**
   ```bash
   # 如果有Go环境，重新编译
   go build -o bin/semaphore ./cli/main.go

   # 重启服务器
   ./bin/semaphore server --config config.json
   ```

### 完整实现 (1-2小时)

1. 按照方法2实现完整的API功能
2. 添加必要的数据库迁移
3. 测试所有运行器相关功能
4. 添加错误处理和验证

## ⚠️ 注意事项

1. **许可证合规**: 确保您的使用符合MIT许可证条款
2. **功能完整性**: 开源版可能缺少一些专业版的高级功能
3. **维护成本**: 自己实现需要持续维护和更新
4. **技术支持**: 修改后可能无法获得官方技术支持

## 🎯 推荐方案

对于学习和开发环境，推荐使用 **方法1 (修改构建标签)**，这是最简单快速的方式。

对于生产环境，建议考虑购买专业版以获得完整功能和技术支持。

## 📝 实际操作示例

### 立即启用项目级运行器

以下是一个完整的启用脚本：

```bash
#!/bin/bash
# enable-project-runners.sh

echo "🔧 启用 Semaphore 项目级运行器功能..."

# 1. 备份原始文件
echo "📦 备份原始文件..."
cp api/router.go api/router.go.backup
cp api/projects/runners.go api/projects/runners.go.backup
cp db/bolt/runner.go db/bolt/runner.go.backup
cp db/sql/runner.go db/sql/runner.go.backup

# 2. 修改功能标志
echo "🚀 启用前端功能标志..."
sed -i 's/"project_runners":   false/"project_runners":   true/' api/router.go

# 3. 移除构建标签限制
echo "🔓 移除API限制..."
sed -i '/^\/\/go:build !pro$/d' api/projects/runners.go
sed -i '/^\/\/go:build !pro$/d' db/bolt/runner.go
sed -i '/^\/\/go:build !pro$/d' db/sql/runner.go

# 4. 重新编译 (如果有Go环境)
if command -v go &> /dev/null; then
    echo "🔨 重新编译..."
    go build -o bin/semaphore ./cli/main.go
    echo "✅ 编译完成"
else
    echo "⚠️  未找到Go环境，请手动重新编译"
fi

echo "🎉 项目级运行器功能已启用！"
echo "📋 请重启Semaphore服务器以应用更改"
```

### 恢复原始功能

```bash
#!/bin/bash
# restore-original.sh

echo "🔄 恢复原始Semaphore功能..."

# 恢复备份文件
cp api/router.go.backup api/router.go
cp api/projects/runners.go.backup api/projects/runners.go
cp db/bolt/runner.go.backup db/bolt/runner.go
cp db/sql/runner.go.backup db/sql/runner.go

# 重新编译
if command -v go &> /dev/null; then
    go build -o bin/semaphore ./cli/main.go
fi

echo "✅ 已恢复原始功能"
```

## 🔍 深入分析：专业版限制机制

### 1. 编译时限制 vs 运行时限制

**Semaphore 的特点：**
- ✅ 使用编译时限制（构建标签）
- ❌ 没有运行时许可证验证
- ❌ 没有在线激活检查
- ❌ 没有加密的许可证文件

这意味着限制是在代码编译阶段实现的，而不是在运行时检查许可证。

### 2. 构建标签工作原理

```go
//go:build !pro
// 这个文件只在非专业版构建时包含

//go:build pro
// 这个文件只在专业版构建时包含
```

**编译命令差异：**
```bash
# 开源版编译
go build -tags="" ./cli/main.go

# 专业版编译
go build -tags="pro" ./cli/main.go
```

### 3. 限制实现的层次

```
前端 (Vue.js)
├── 功能标志检查 (premiumFeatures.project_runners)
├── UI元素隐藏/显示
└── 专业版提示信息

API层 (Go)
├── 路由处理器返回404
├── 功能标志设置为false
└── 中间件限制

数据库层 (Go)
├── 查询函数返回ErrNotFound
├── 创建/更新/删除操作被禁用
└── 数据访问完全阻断
```

### 4. 其他专业版功能

通过代码分析发现的其他专业版功能：

1. **Terraform 后端支持**
   - 文件：`api/terraform.go` (带 `!pro` 标签)
   - 功能：HTTP 后端存储状态

2. **高级日志功能**
   - 文件：`api/helpers/event_file_log.go` (带 `!pro` 标签)
   - 功能：文件日志记录

3. **模板别名功能**
   - 文件：`db/Template_alias.go` (带 `!pro` 标签)
   - 功能：模板别名管理

4. **任务钩子工厂**
   - 文件：`services/tasks/hooks/factory.go` (带 `!pro` 标签)
   - 功能：任务执行钩子

## 🛡️ 安全和合规考虑

### 1. MIT 许可证分析

Semaphore 使用 MIT 许可证，这意味着：

✅ **允许的操作：**
- 商业使用
- 修改代码
- 分发
- 私人使用

⚠️ **要求：**
- 保留版权声明
- 保留许可证文本

❌ **不提供：**
- 责任保证
- 技术支持保证

### 2. 修改建议

**学习/开发环境：**
- ✅ 可以自由修改和使用
- ✅ 有助于理解系统架构
- ✅ 可以贡献开源社区

**生产环境：**
- ⚠️ 考虑技术支持需求
- ⚠️ 考虑功能完整性
- ⚠️ 考虑长期维护成本

## 🔧 高级实现方案

### 方案4: 动态功能控制

创建一个更灵活的功能控制系统：

```go
// util/features.go
package util

type FeatureFlags struct {
    ProjectRunners   bool `json:"project_runners"`
    TerraformBackend bool `json:"terraform_backend"`
    AdvancedLogging  bool `json:"advanced_logging"`
}

var Features = FeatureFlags{
    ProjectRunners:   GetEnvBool("SEMAPHORE_ENABLE_PROJECT_RUNNERS", false),
    TerraformBackend: GetEnvBool("SEMAPHORE_ENABLE_TERRAFORM_BACKEND", false),
    AdvancedLogging:  GetEnvBool("SEMAPHORE_ENABLE_ADVANCED_LOGGING", false),
}

func GetEnvBool(key string, defaultValue bool) bool {
    if value := os.Getenv(key); value != "" {
        if parsed, err := strconv.ParseBool(value); err == nil {
            return parsed
        }
    }
    return defaultValue
}
```

然后在各个模块中使用：

```go
// api/router.go
"premium_features": map[string]bool{
    "project_runners":   util.Features.ProjectRunners,
    "terraform_backend": util.Features.TerraformBackend,
},

// api/projects/runners.go
func AddRunner(w http.ResponseWriter, r *http.Request) {
    if !util.Features.ProjectRunners {
        w.WriteHeader(http.StatusNotFound)
        return
    }
    // 实际实现...
}
```

### 启动配置

```bash
# 启用所有功能
export SEMAPHORE_ENABLE_PROJECT_RUNNERS=true
export SEMAPHORE_ENABLE_TERRAFORM_BACKEND=true
export SEMAPHORE_ENABLE_ADVANCED_LOGGING=true

./bin/semaphore server --config config.json
```

## 📊 总结对比

| 方案 | 难度 | 时间 | 灵活性 | 维护性 | 推荐度 |
|------|------|------|--------|--------|--------|
| 修改构建标签 | ⭐ | 5分钟 | ⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐ |
| 完整重新实现 | ⭐⭐⭐⭐ | 2小时 | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐ |
| 环境变量控制 | ⭐⭐ | 30分钟 | ⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 动态功能控制 | ⭐⭐⭐ | 1小时 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |

**最终推荐：** 对于快速验证使用方案1，对于长期使用推荐方案3（环境变量控制）。
