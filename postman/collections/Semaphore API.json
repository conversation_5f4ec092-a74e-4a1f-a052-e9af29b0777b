{"info": {"_postman_id": "3d96a8d7-604d-47ec-832c-2876f64dbc1f", "name": "Semaphore API", "description": "Semaphore API provides endpoints for managing and interacting with the Semaphore UI.\nThis documentation outlines the available operations and data models.\n", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_uid": "2979975-3d96a8d7-604d-47ec-832c-2876f64dbc1f"}, "item": [{"name": "ping", "item": [{"name": "PING test", "id": "bf5268d0-7ded-46a5-81c6-e1ab34af548f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "text/plain"}], "url": {"raw": "{{baseUrl}}/ping", "host": ["{{baseUrl}}"], "path": ["ping"]}}, "response": [{"id": "3bb6eb99-c489-4c1d-8c58-24943b3944fa", "name": "Successful \"PONG\" reply", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "text/plain"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/ping", "host": ["{{baseUrl}}"], "path": ["ping"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}, {"disabled": false, "description": {"content": "", "type": "text/plain"}, "key": "content-type", "value": "<string>"}], "cookie": [], "body": "<string>"}]}], "id": "42b892bf-1031-4529-aaad-fc9cb0f0b39b"}, {"name": "ws", "item": [{"name": "Websocket handler", "id": "0c5fb481-c1ae-4749-922b-9c204542ebe5", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/ws", "host": ["{{baseUrl}}"], "path": ["ws"]}}, "response": [{"id": "806acad8-300d-49d7-b90d-efa215abf78b", "name": "OK", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/ws", "host": ["{{baseUrl}}"], "path": ["ws"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [], "cookie": []}, {"id": "3ddd1e5f-77ca-4004-a6df-6fba17dd91e3", "name": "not authenticated", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/ws", "host": ["{{baseUrl}}"], "path": ["ws"]}}, "status": "Unauthorized", "code": 401, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "c4ce3efb-64a9-419e-ae7b-710d8daa6559"}, {"name": "info", "item": [{"name": "Fetches information about semaphore", "id": "b24f5bef-31a9-4b6b-a88b-7335708054b4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/info", "host": ["{{baseUrl}}"], "path": ["info"]}, "description": "you must be authenticated to use this"}, "response": [{"id": "528e9198-1331-47b6-8683-26a4dee42897", "name": "ok", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/info", "host": ["{{baseUrl}}"], "path": ["info"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"version\": \"<string>\",\n  \"updateBody\": \"<string>\",\n  \"update\": {\n    \"tag_name\": \"<string>\"\n  }\n}"}]}], "id": "f3b4c4c0-0266-46de-bfd9-ec0ca92cb8ed"}, {"name": "auth", "item": [{"name": "login", "item": [{"name": "Fetches login metadata", "id": "e54ef912-9074-4d4b-ac87-4a139deaae7e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}, "description": "Fetches metadata for login, such as available OIDC providers"}, "response": [{"id": "88c4efe4-14a7-4995-9f4e-5d799df9691c", "name": "Login metadata", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"oidc_providers\": [\n    {\n      \"id\": \"<string>\",\n      \"name\": \"<string>\"\n    },\n    {\n      \"id\": \"<string>\",\n      \"name\": \"<string>\"\n    }\n  ]\n}"}]}, {"name": "Performs Login", "id": "8e4e1cca-d595-4648-9926-cc59aa0d01c0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"auth\": \"<string>\",\n  \"password\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}, "description": "Upon success you will be logged in"}, "response": [{"id": "d46ea9c6-566d-4797-8257-481ea3ac387d", "name": "You are logged in", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"auth\": \"<string>\",\n  \"password\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}, {"id": "d924516a-a87b-4e37-b0ee-37b861d55e44", "name": "something in body is missing / is invalid", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"auth\": \"<string>\",\n  \"password\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "aed9631c-e197-4069-85fd-d104db4a2935"}, {"name": "logout", "item": [{"name": "Destroys current session", "id": "40410efc-af5e-427e-a028-f97718cb6836", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/auth/logout", "host": ["{{baseUrl}}"], "path": ["auth", "logout"]}}, "response": [{"id": "67a542a6-eeab-4de1-8c4d-f4c97f061b40", "name": "Your session was successfully nuked", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/auth/logout", "host": ["{{baseUrl}}"], "path": ["auth", "logout"]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "56f5320b-b581-4f77-bc09-c6ff2b6650fe"}, {"name": "oidc", "item": [{"name": "{provider_id}", "item": [{"name": "login", "item": [{"name": "Begin OIDC authentication flow and redirect to OIDC provider", "id": "e89b42ec-0f75-424d-983f-ec083a25629e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/oidc/:provider_id/login", "host": ["{{baseUrl}}"], "path": ["auth", "oidc", ":provider_id", "login"], "variable": [{"key": "provider_id", "value": "<string>", "description": "(Required) "}]}, "description": "The user agent is redirected to this endpoint when chosing to sign in via OIDC"}, "response": [{"id": "dc9ff176-1520-4d23-870e-6de15f0f0bfb", "name": "Redirection to the OIDC provider on success, or to the login page on error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/auth/oidc/:provider_id/login", "host": ["{{baseUrl}}"], "path": ["auth", "oidc", ":provider_id", "login"], "variable": [{"key": "provider_id"}]}}, "status": "Found", "code": 302, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "02b6aaf2-f76d-4f7e-9eef-7c1969106934"}, {"name": "redirect", "item": [{"name": "Finish OIDC authentication flow, upon succes you will be logged in", "id": "a3f83744-c4b2-444d-b67d-0b9e1719b579", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/auth/oidc/:provider_id/redirect", "host": ["{{baseUrl}}"], "path": ["auth", "oidc", ":provider_id", "redirect"], "variable": [{"key": "provider_id", "value": "<string>", "description": "(Required) "}]}, "description": "The user agent is redirected here by the OIDC provider to complete authentication"}, "response": [{"id": "c961634e-53b3-464d-8700-1f7a50bd8930", "name": "Redirection to the Semaphore root URL on success, or to the login page on error", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/auth/oidc/:provider_id/redirect", "host": ["{{baseUrl}}"], "path": ["auth", "oidc", ":provider_id", "redirect"], "variable": [{"key": "provider_id"}]}}, "status": "Found", "code": 302, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "09c8fe31-4e03-4951-9cfc-b35eb5788fdc"}], "id": "4eb69a41-dc94-4d9a-ac10-b4052fefa5e0"}], "id": "4097fae7-9877-4776-a607-4a91236eb004"}], "id": "1c92c5b1-ef55-4bf0-8b0e-942fd4dbd4d6"}, {"name": "user", "item": [{"name": "tokens", "item": [{"name": "{api_token_id}", "item": [{"name": "Expires API token", "id": "b73a8391-ac14-4de1-96e4-46211d2695a3", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/user/tokens/:api_token_id", "host": ["{{baseUrl}}"], "path": ["user", "tokens", ":api_token_id"], "variable": [{"key": "api_token_id", "value": "<string>", "description": "(Required) "}]}}, "response": [{"id": "b065338e-0893-4b2e-a114-0fbebe9df480", "name": "Expired API Token", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/user/tokens/:api_token_id", "host": ["{{baseUrl}}"], "path": ["user", "tokens", ":api_token_id"], "variable": [{"key": "api_token_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "16a03dcb-86f4-4b13-9b0b-2ee6e40f277f"}, {"name": "Fetch API tokens for user", "id": "72430204-c78a-404d-a8b5-cf9b9535e8ec", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/user/tokens", "host": ["{{baseUrl}}"], "path": ["user", "tokens"]}}, "response": [{"id": "a29abb44-c40e-4170-8715-7355facafe4f", "name": "API Tokens", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/user/tokens", "host": ["{{baseUrl}}"], "path": ["user", "tokens"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": \"<string>\",\n    \"created\": \"<string>\",\n    \"expired\": \"<boolean>\",\n    \"user_id\": \"<integer>\"\n  },\n  {\n    \"id\": \"<string>\",\n    \"created\": \"<string>\",\n    \"expired\": \"<boolean>\",\n    \"user_id\": \"<integer>\"\n  }\n]"}]}, {"name": "Create an API token", "id": "cfba220e-2ace-4bcd-8e8d-790ecc4c161b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/user/tokens", "host": ["{{baseUrl}}"], "path": ["user", "tokens"]}}, "response": [{"id": "ab320df0-a87c-4d98-8c66-b746965d8703", "name": "API Token", "originalRequest": {"method": "POST", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/user/tokens", "host": ["{{baseUrl}}"], "path": ["user", "tokens"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<string>\",\n  \"created\": \"<string>\",\n  \"expired\": \"<boolean>\",\n  \"user_id\": \"<integer>\"\n}"}]}], "id": "1dbfa3fd-5095-40b3-b767-9905075c41cc"}, {"name": "Fetch logged in user", "id": "fef2dcea-c231-4700-86e3-352a7509428f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/user/", "host": ["{{baseUrl}}"], "path": ["user", ""]}}, "response": [{"id": "8ac63913-9e63-4e07-9ce1-14e07dc34d20", "name": "User", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/user/", "host": ["{{baseUrl}}"], "path": ["user", ""]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"username\": \"<string>\",\n  \"email\": \"<string>\",\n  \"created\": \"<string>\",\n  \"alert\": \"<boolean>\",\n  \"admin\": \"<boolean>\",\n  \"external\": \"<boolean>\"\n}"}]}], "id": "320427cd-4a4a-4466-a69a-32854273b1b2"}, {"name": "users", "item": [{"name": "{user_id}", "item": [{"name": "password", "item": [{"name": "Updates user password", "id": "049ff59a-80e1-4b3d-b5c8-c153f0876b94", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"password\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/users/:user_id/password", "host": ["{{baseUrl}}"], "path": ["users", ":user_id", "password"], "variable": [{"key": "user_id", "value": "<integer>", "description": "(Required) User ID"}]}}, "response": [{"id": "86c57d98-9b33-442d-be6e-700f3374685d", "name": "Password updated", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"password\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/users/:user_id/password", "host": ["{{baseUrl}}"], "path": ["users", ":user_id", "password"], "variable": [{"key": "user_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "ff5732a1-5d72-4dcf-8d13-cb1a0352d82b"}, {"name": "Fetches a user profile", "id": "0ea82762-7ab3-4be4-a7bc-07c12c6443cd", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/users/:user_id/", "host": ["{{baseUrl}}"], "path": ["users", ":user_id", ""], "variable": [{"key": "user_id", "value": "<integer>", "description": "(Required) User ID"}]}}, "response": [{"id": "5a9e436e-a27e-490c-aa04-2931ec671450", "name": "User profile", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/users/:user_id/", "host": ["{{baseUrl}}"], "path": ["users", ":user_id", ""], "variable": [{"key": "user_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"username\": \"<string>\",\n  \"email\": \"<string>\",\n  \"created\": \"<string>\",\n  \"alert\": \"<boolean>\",\n  \"admin\": \"<boolean>\",\n  \"external\": \"<boolean>\"\n}"}]}, {"name": "Updates user details", "id": "f16b9520-e006-4f1c-a1d6-e1c112a6c737", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<string>\",\n  \"username\": \"<string>\",\n  \"email\": \"<string>\",\n  \"alert\": \"<boolean>\",\n  \"admin\": \"<boolean>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/users/:user_id/", "host": ["{{baseUrl}}"], "path": ["users", ":user_id", ""], "variable": [{"key": "user_id", "value": "<integer>", "description": "(Required) User ID"}]}}, "response": [{"id": "8ecb3f54-ba8a-454a-af8a-bd45b3487d81", "name": "User Updated", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<string>\",\n  \"username\": \"<string>\",\n  \"email\": \"<string>\",\n  \"alert\": \"<boolean>\",\n  \"admin\": \"<boolean>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/users/:user_id/", "host": ["{{baseUrl}}"], "path": ["users", ":user_id", ""], "variable": [{"key": "user_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}, {"name": "Deletes user", "id": "ab4724a5-0e60-4c1a-a097-cb43677e968f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/users/:user_id/", "host": ["{{baseUrl}}"], "path": ["users", ":user_id", ""], "variable": [{"key": "user_id", "value": "<integer>", "description": "(Required) User ID"}]}}, "response": [{"id": "f7a4e7b7-740f-425c-b9bb-238c1e77a2b2", "name": "User deleted", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/users/:user_id/", "host": ["{{baseUrl}}"], "path": ["users", ":user_id", ""], "variable": [{"key": "user_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "a1b50f3a-4823-4aec-a930-4dc2eb3e0d5d"}, {"name": "Fetches all users", "id": "507c369c-033e-4267-8bf2-ae66c9ac3599", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/users", "host": ["{{baseUrl}}"], "path": ["users"]}}, "response": [{"id": "99e6869a-f9a1-4255-9000-bddace414eed", "name": "Users", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/users", "host": ["{{baseUrl}}"], "path": ["users"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": \"<integer>\",\n    \"name\": \"<string>\",\n    \"username\": \"<string>\",\n    \"email\": \"<string>\",\n    \"created\": \"<string>\",\n    \"alert\": \"<boolean>\",\n    \"admin\": \"<boolean>\",\n    \"external\": \"<boolean>\"\n  },\n  {\n    \"id\": \"<integer>\",\n    \"name\": \"<string>\",\n    \"username\": \"<string>\",\n    \"email\": \"<string>\",\n    \"created\": \"<string>\",\n    \"alert\": \"<boolean>\",\n    \"admin\": \"<boolean>\",\n    \"external\": \"<boolean>\"\n  }\n]"}]}, {"name": "Creates a user", "id": "1f9aeee5-29d9-45a5-a8c2-85cc1837a96a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<string>\",\n  \"username\": \"<string>\",\n  \"email\": \"<string>\",\n  \"password\": \"<string>\",\n  \"alert\": \"<boolean>\",\n  \"admin\": \"<boolean>\",\n  \"external\": \"<boolean>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/users", "host": ["{{baseUrl}}"], "path": ["users"]}}, "response": [{"id": "5056ee51-12cb-45ac-97ec-2007d3ea55c1", "name": "User created", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<string>\",\n  \"username\": \"<string>\",\n  \"email\": \"<string>\",\n  \"password\": \"<string>\",\n  \"alert\": \"<boolean>\",\n  \"admin\": \"<boolean>\",\n  \"external\": \"<boolean>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/users", "host": ["{{baseUrl}}"], "path": ["users"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"username\": \"<string>\",\n  \"email\": \"<string>\",\n  \"created\": \"<string>\",\n  \"alert\": \"<boolean>\",\n  \"admin\": \"<boolean>\",\n  \"external\": \"<boolean>\"\n}"}, {"id": "07889849-9235-4c9b-b0a1-8f208cfe0f68", "name": "User creation failed", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<string>\",\n  \"username\": \"<string>\",\n  \"email\": \"<string>\",\n  \"password\": \"<string>\",\n  \"alert\": \"<boolean>\",\n  \"admin\": \"<boolean>\",\n  \"external\": \"<boolean>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/users", "host": ["{{baseUrl}}"], "path": ["users"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "daedae70-868f-4c67-b451-92b64d11f389"}, {"name": "projects", "item": [{"name": "restore", "item": [{"name": "Restore Project", "id": "ad7d9848-0a4c-4429-8f00-a9c41d0b8b65", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"meta\": {\n    \"name\": \"<string>\",\n    \"alert\": \"<boolean>\",\n    \"max_parallel_tasks\": \"<integer>\"\n  },\n  \"templates\": [\n    {\n      \"inventory\": \"<string>\",\n      \"repository\": \"<string>\",\n      \"environment\": \"<string>\",\n      \"view\": \"<string>\",\n      \"name\": \"<string>\",\n      \"playbook\": \"<string>\",\n      \"description\": \"<string>\",\n      \"allow_override_args_in_task\": \"<boolean>\",\n      \"suppress_success_alerts\": \"<boolean>\",\n      \"autorun\": \"<boolean>\",\n      \"type\": \"<string>\",\n      \"allow_override_branch_in_task\": \"<boolean>\"\n    },\n    {\n      \"inventory\": \"<string>\",\n      \"repository\": \"<string>\",\n      \"environment\": \"<string>\",\n      \"view\": \"<string>\",\n      \"name\": \"<string>\",\n      \"playbook\": \"<string>\",\n      \"description\": \"<string>\",\n      \"allow_override_args_in_task\": \"<boolean>\",\n      \"suppress_success_alerts\": \"<boolean>\",\n      \"autorun\": \"<boolean>\",\n      \"type\": \"<string>\",\n      \"allow_override_branch_in_task\": \"<boolean>\"\n    }\n  ],\n  \"repositories\": [\n    {\n      \"name\": \"<string>\",\n      \"git_url\": \"<string>\",\n      \"git_branch\": \"<string>\",\n      \"ssh_key\": \"<string>\"\n    },\n    {\n      \"name\": \"<string>\",\n      \"git_url\": \"<string>\",\n      \"git_branch\": \"<string>\",\n      \"ssh_key\": \"<string>\"\n    }\n  ],\n  \"keys\": [\n    {\n      \"name\": \"<string>\",\n      \"type\": \"login_password\"\n    },\n    {\n      \"name\": \"<string>\",\n      \"type\": \"ssh\"\n    }\n  ],\n  \"views\": [\n    {\n      \"name\": \"<string>\",\n      \"position\": \"<integer>\"\n    },\n    {\n      \"name\": \"<string>\",\n      \"position\": \"<integer>\"\n    }\n  ],\n  \"inventories\": [\n    {\n      \"name\": \"<string>\",\n      \"inventory\": \"<string>\",\n      \"type\": \"static-yaml\"\n    },\n    {\n      \"name\": \"<string>\",\n      \"inventory\": \"<string>\",\n      \"type\": \"static\"\n    }\n  ],\n  \"environments\": [\n    {\n      \"name\": \"<string>\",\n      \"json\": \"<string>\"\n    },\n    {\n      \"name\": \"<string>\",\n      \"json\": \"<string>\"\n    }\n  ]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/projects/restore", "host": ["{{baseUrl}}"], "path": ["projects", "restore"]}}, "response": [{"id": "8120a0a7-787f-47f5-b595-dd8dab10efd4", "name": "Created project", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"meta\": {\n    \"name\": \"<string>\",\n    \"alert\": \"<boolean>\",\n    \"max_parallel_tasks\": \"<integer>\"\n  },\n  \"templates\": [\n    {\n      \"inventory\": \"<string>\",\n      \"repository\": \"<string>\",\n      \"environment\": \"<string>\",\n      \"view\": \"<string>\",\n      \"name\": \"<string>\",\n      \"playbook\": \"<string>\",\n      \"description\": \"<string>\",\n      \"allow_override_args_in_task\": \"<boolean>\",\n      \"suppress_success_alerts\": \"<boolean>\",\n      \"autorun\": \"<boolean>\",\n      \"type\": \"<string>\",\n      \"allow_override_branch_in_task\": \"<boolean>\"\n    },\n    {\n      \"inventory\": \"<string>\",\n      \"repository\": \"<string>\",\n      \"environment\": \"<string>\",\n      \"view\": \"<string>\",\n      \"name\": \"<string>\",\n      \"playbook\": \"<string>\",\n      \"description\": \"<string>\",\n      \"allow_override_args_in_task\": \"<boolean>\",\n      \"suppress_success_alerts\": \"<boolean>\",\n      \"autorun\": \"<boolean>\",\n      \"type\": \"<string>\",\n      \"allow_override_branch_in_task\": \"<boolean>\"\n    }\n  ],\n  \"repositories\": [\n    {\n      \"name\": \"<string>\",\n      \"git_url\": \"<string>\",\n      \"git_branch\": \"<string>\",\n      \"ssh_key\": \"<string>\"\n    },\n    {\n      \"name\": \"<string>\",\n      \"git_url\": \"<string>\",\n      \"git_branch\": \"<string>\",\n      \"ssh_key\": \"<string>\"\n    }\n  ],\n  \"keys\": [\n    {\n      \"name\": \"<string>\",\n      \"type\": \"login_password\"\n    },\n    {\n      \"name\": \"<string>\",\n      \"type\": \"ssh\"\n    }\n  ],\n  \"views\": [\n    {\n      \"name\": \"<string>\",\n      \"position\": \"<integer>\"\n    },\n    {\n      \"name\": \"<string>\",\n      \"position\": \"<integer>\"\n    }\n  ],\n  \"inventories\": [\n    {\n      \"name\": \"<string>\",\n      \"inventory\": \"<string>\",\n      \"type\": \"static-yaml\"\n    },\n    {\n      \"name\": \"<string>\",\n      \"inventory\": \"<string>\",\n      \"type\": \"static\"\n    }\n  ],\n  \"environments\": [\n    {\n      \"name\": \"<string>\",\n      \"json\": \"<string>\"\n    },\n    {\n      \"name\": \"<string>\",\n      \"json\": \"<string>\"\n    }\n  ]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/projects/restore", "host": ["{{baseUrl}}"], "path": ["projects", "restore"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"created\": \"<string>\",\n  \"alert\": \"<boolean>\",\n  \"max_parallel_tasks\": \"<integer>\"\n}"}]}], "id": "75f5f172-970b-46f9-9749-ea60fe0de02f"}, {"name": "Get projects", "id": "db08f6e5-34e8-4cce-b433-a8e001649532", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/projects", "host": ["{{baseUrl}}"], "path": ["projects"]}}, "response": [{"id": "4465385b-232d-49e1-b0ba-f076805edba2", "name": "List of projects", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/projects", "host": ["{{baseUrl}}"], "path": ["projects"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": \"<integer>\",\n    \"name\": \"<string>\",\n    \"created\": \"<string>\",\n    \"alert\": \"<boolean>\",\n    \"max_parallel_tasks\": \"<integer>\"\n  },\n  {\n    \"id\": \"<integer>\",\n    \"name\": \"<string>\",\n    \"created\": \"<string>\",\n    \"alert\": \"<boolean>\",\n    \"max_parallel_tasks\": \"<integer>\"\n  }\n]"}]}, {"name": "Create a new project", "id": "d836bcbc-10a2-48fd-a189-f7e2d546f7a0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<string>\",\n  \"alert\": \"<boolean>\",\n  \"max_parallel_tasks\": \"<integer>\",\n  \"demo\": \"<boolean>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/projects", "host": ["{{baseUrl}}"], "path": ["projects"]}}, "response": [{"id": "85c1a2ae-bfcb-410e-9ef1-10f33ce9a4a3", "name": "Created project", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<string>\",\n  \"alert\": \"<boolean>\",\n  \"max_parallel_tasks\": \"<integer>\",\n  \"demo\": \"<boolean>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/projects", "host": ["{{baseUrl}}"], "path": ["projects"]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"created\": \"<string>\",\n  \"alert\": \"<boolean>\",\n  \"max_parallel_tasks\": \"<integer>\"\n}"}]}], "id": "1ca11787-296e-4f18-8098-b3863a3697ae"}, {"name": "events", "item": [{"name": "last", "item": [{"name": "Get last 200 Events related to Semaphore and projects you are part of", "id": "716eed97-b5c6-436b-9815-13ee33dfb791", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/events/last", "host": ["{{baseUrl}}"], "path": ["events", "last"]}}, "response": [{"id": "194afa5f-39ef-4da7-8f57-f3646379e74c", "name": "Array of events in chronological order", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/events/last", "host": ["{{baseUrl}}"], "path": ["events", "last"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"project_id\": \"<integer>\",\n    \"user_id\": \"<integer>\",\n    \"description\": \"<string>\"\n  },\n  {\n    \"project_id\": \"<integer>\",\n    \"user_id\": \"<integer>\",\n    \"description\": \"<string>\"\n  }\n]"}]}], "id": "9aa87430-581f-4df2-b5b0-0e907eb51d8c"}, {"name": "Get Events related to Semaphore and projects you are part of", "id": "8892a736-a9e9-4e04-9fbf-7318e18bcb69", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/events", "host": ["{{baseUrl}}"], "path": ["events"]}}, "response": [{"id": "0df3b266-9969-4b81-9c49-5365b5718f7c", "name": "Array of events in chronological order", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/events", "host": ["{{baseUrl}}"], "path": ["events"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"project_id\": \"<integer>\",\n    \"user_id\": \"<integer>\",\n    \"description\": \"<string>\"\n  },\n  {\n    \"project_id\": \"<integer>\",\n    \"user_id\": \"<integer>\",\n    \"description\": \"<string>\"\n  }\n]"}]}], "id": "633c1663-c41d-4cbe-813e-c86e157d7b1f"}, {"name": "project", "item": [{"name": "{project_id}", "item": [{"name": "backup", "item": [{"name": "Backup A Project", "id": "cf6cb225-f9c2-4834-9966-fba7247c941c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/backup", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "backup"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "9ed64511-9b95-4241-b139-521f01d7a9fe", "name": "Backup", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/backup", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "backup"], "variable": [{"key": "project_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"meta\": {\n    \"name\": \"<string>\",\n    \"alert\": \"<boolean>\",\n    \"max_parallel_tasks\": \"<integer>\"\n  },\n  \"templates\": [\n    {\n      \"inventory\": \"<string>\",\n      \"repository\": \"<string>\",\n      \"environment\": \"<string>\",\n      \"view\": \"<string>\",\n      \"name\": \"<string>\",\n      \"playbook\": \"<string>\",\n      \"description\": \"<string>\",\n      \"allow_override_args_in_task\": \"<boolean>\",\n      \"suppress_success_alerts\": \"<boolean>\",\n      \"autorun\": \"<boolean>\",\n      \"type\": \"<string>\",\n      \"allow_override_branch_in_task\": \"<boolean>\"\n    },\n    {\n      \"inventory\": \"<string>\",\n      \"repository\": \"<string>\",\n      \"environment\": \"<string>\",\n      \"view\": \"<string>\",\n      \"name\": \"<string>\",\n      \"playbook\": \"<string>\",\n      \"description\": \"<string>\",\n      \"allow_override_args_in_task\": \"<boolean>\",\n      \"suppress_success_alerts\": \"<boolean>\",\n      \"autorun\": \"<boolean>\",\n      \"type\": \"<string>\",\n      \"allow_override_branch_in_task\": \"<boolean>\"\n    }\n  ],\n  \"repositories\": [\n    {\n      \"name\": \"<string>\",\n      \"git_url\": \"<string>\",\n      \"git_branch\": \"<string>\",\n      \"ssh_key\": \"<string>\"\n    },\n    {\n      \"name\": \"<string>\",\n      \"git_url\": \"<string>\",\n      \"git_branch\": \"<string>\",\n      \"ssh_key\": \"<string>\"\n    }\n  ],\n  \"keys\": [\n    {\n      \"name\": \"<string>\",\n      \"type\": \"login_password\"\n    },\n    {\n      \"name\": \"<string>\",\n      \"type\": \"ssh\"\n    }\n  ],\n  \"views\": [\n    {\n      \"name\": \"<string>\",\n      \"position\": \"<integer>\"\n    },\n    {\n      \"name\": \"<string>\",\n      \"position\": \"<integer>\"\n    }\n  ],\n  \"inventories\": [\n    {\n      \"name\": \"<string>\",\n      \"inventory\": \"<string>\",\n      \"type\": \"static-yaml\"\n    },\n    {\n      \"name\": \"<string>\",\n      \"inventory\": \"<string>\",\n      \"type\": \"static\"\n    }\n  ],\n  \"environments\": [\n    {\n      \"name\": \"<string>\",\n      \"json\": \"<string>\"\n    },\n    {\n      \"name\": \"<string>\",\n      \"json\": \"<string>\"\n    }\n  ]\n}"}]}], "id": "5d358a00-f511-471c-abfe-4fa03d4aae12"}, {"name": "role", "item": [{"name": "Fetch permissions of the current user for project", "id": "acf0720e-0c03-4c38-b272-c9465782f650", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/role", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "role"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "6ebbb1a2-26f5-49bd-95c1-eb91f9ce555c", "name": "Permissions", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/role", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "role"], "variable": [{"key": "project_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"role\": \"<string>\",\n  \"permissions\": \"<number>\"\n}"}]}], "id": "f38f0344-5967-413a-afc9-3b23f8f29ff9"}, {"name": "events", "item": [{"name": "Get Events related to this project", "id": "b0e3b287-b69c-4164-9e5f-99285e393c7f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/events", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "events"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "bb0b55e4-3368-4841-beb9-fdcf9194bc6c", "name": "Array of events in chronological order", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/events", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "events"], "variable": [{"key": "project_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"project_id\": \"<integer>\",\n    \"user_id\": \"<integer>\",\n    \"description\": \"<string>\"\n  },\n  {\n    \"project_id\": \"<integer>\",\n    \"user_id\": \"<integer>\",\n    \"description\": \"<string>\"\n  }\n]"}]}], "id": "59ab5972-7a4b-4796-b0b9-c3b3409533d8"}, {"name": "users", "item": [{"name": "{user_id}", "item": [{"name": "Update user role", "id": "ed021401-fce4-4cd5-8bbd-3ac2bcd23f8f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"role\": \"manager\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/users/:user_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "users", ":user_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "user_id", "value": "<integer>", "description": "(Required) User ID"}]}}, "response": [{"id": "8159e830-af1a-4567-9427-59a8c19b1022", "name": "User updated", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"role\": \"manager\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/users/:user_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "users", ":user_id"], "variable": [{"key": "project_id"}, {"key": "user_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}, {"name": "Removes user from project", "id": "12114ff7-c872-4699-ae98-535db41cbab0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/project/:project_id/users/:user_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "users", ":user_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "user_id", "value": "<integer>", "description": "(Required) User ID"}]}}, "response": [{"id": "65245997-98d2-4ca9-b5f7-bbe8a54c5f07", "name": "User removed", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/users/:user_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "users", ":user_id"], "variable": [{"key": "project_id"}, {"key": "user_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "93b0c15b-1858-42e0-9a4b-d5714fde836b"}, {"name": "Get users linked to project", "id": "0fbedd92-e7f1-4207-9952-1fa11841630d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/users?sort=email&order=asc", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "users"], "query": [{"key": "sort", "value": "email", "description": "(Required) sorting name"}, {"key": "order", "value": "asc", "description": "(Required) ordering manner"}], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "ee4a34bb-2053-461c-bdef-479d47ca6586", "name": "Users", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/users?sort=email&order=asc", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "users"], "query": [{"description": "(Required) sorting name", "key": "sort", "value": "email"}, {"description": "(Required) ordering manner", "key": "order", "value": "asc"}], "variable": [{"key": "project_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": \"<integer>\",\n    \"name\": \"<string>\",\n    \"username\": \"<string>\",\n    \"role\": \"task_runner\"\n  },\n  {\n    \"id\": \"<integer>\",\n    \"name\": \"<string>\",\n    \"username\": \"<string>\",\n    \"role\": \"guest\"\n  }\n]"}]}, {"name": "Link user to project", "id": "97f0f4f1-d226-43d8-8c77-ffd4ac2baa2d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"<integer>\",\n  \"role\": \"guest\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/users", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "users"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "53628ba9-1548-4f99-8d47-3de43502cba4", "name": "User added", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"user_id\": \"<integer>\",\n  \"role\": \"guest\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/users", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "users"], "variable": [{"key": "project_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "b25959e0-7fde-4233-a588-4685ccef8d96"}, {"name": "integrations", "item": [{"name": "{integration_id}", "item": [{"name": "values", "item": [{"name": "{extractvalue_id}", "item": [{"name": "Updates Integration ExtractValue", "id": "7bc07454-80f6-4b60-ba54-2d2a7f1a41c4", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<string>\",\n  \"value_source\": \"body\",\n  \"body_data_type\": \"xml\",\n  \"key\": \"<string>\",\n  \"variable\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id/values/:extractvalue_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id", "values", ":extractvalue_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "integration_id", "value": "<integer>", "description": "(Required) integration ID"}, {"key": "extractvalue_id", "value": "<integer>", "description": "(Required) extractValue ID"}]}}, "response": [{"id": "bf03df0a-7546-4864-97f9-2ccf4a741e0e", "name": "Integration Extract Value updated", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<string>\",\n  \"value_source\": \"body\",\n  \"body_data_type\": \"xml\",\n  \"key\": \"<string>\",\n  \"variable\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id/values/:extractvalue_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id", "values", ":extractvalue_id"], "variable": [{"key": "project_id"}, {"key": "integration_id"}, {"key": "extractvalue_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}, {"id": "aea21b21-fbef-4bdd-8101-c9caeeaf438c", "name": "Bad integration extract value parameter", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<string>\",\n  \"value_source\": \"body\",\n  \"body_data_type\": \"xml\",\n  \"key\": \"<string>\",\n  \"variable\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id/values/:extractvalue_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id", "values", ":extractvalue_id"], "variable": [{"key": "project_id"}, {"key": "integration_id"}, {"key": "extractvalue_id"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}, {"name": "Removes integration extract value", "id": "2ca39f14-a5e8-49ff-b133-c6c1bca56451", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id/values/:extractvalue_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id", "values", ":extractvalue_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "integration_id", "value": "<integer>", "description": "(Required) integration ID"}, {"key": "extractvalue_id", "value": "<integer>", "description": "(Required) extractValue ID"}]}}, "response": [{"id": "c94d1edb-7b83-4549-b097-3c9c0bac7976", "name": "integration extract value removed", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id/values/:extractvalue_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id", "values", ":extractvalue_id"], "variable": [{"key": "project_id"}, {"key": "integration_id"}, {"key": "extractvalue_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "aa589140-c673-4193-8546-efcc1b05f69f"}, {"name": "Get Integration Extracted Values linked to integration extractor", "id": "5cf57ab1-acbb-46cd-a6b2-d8e6bc40daf2", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id/values", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id", "values"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "integration_id", "value": "<integer>", "description": "(Required) integration ID"}]}}, "response": [{"id": "bd8a3fa9-b988-4e23-8ed2-6b7367bc8e1d", "name": "Integration Extracted Value", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id/values", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id", "values"], "variable": [{"key": "project_id"}, {"key": "integration_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": \"<integer>\",\n    \"name\": \"<string>\",\n    \"value_source\": \"header\",\n    \"body_data_type\": \"xml\",\n    \"key\": \"<string>\",\n    \"variable\": \"<string>\",\n    \"integration_id\": \"<integer>\"\n  },\n  {\n    \"id\": \"<integer>\",\n    \"name\": \"<string>\",\n    \"value_source\": \"body\",\n    \"body_data_type\": \"json\",\n    \"key\": \"<string>\",\n    \"variable\": \"<string>\",\n    \"integration_id\": \"<integer>\"\n  }\n]"}]}, {"name": "Add Integration Extracted Value", "id": "6bdc4a7f-e460-469c-a469-0341b8370ac7", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"value_source\": \"body\",\n  \"body_data_type\": \"json\",\n  \"key\": \"<string>\",\n  \"variable\": \"<string>\",\n  \"integration_id\": \"<integer>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id/values", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id", "values"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "integration_id", "value": "<integer>", "description": "(Required) integration ID"}]}}, "response": [{"id": "1f5d1e06-c87a-4ca8-989d-739850fca285", "name": "Integration Extract Value Created", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"value_source\": \"body\",\n  \"body_data_type\": \"json\",\n  \"key\": \"<string>\",\n  \"variable\": \"<string>\",\n  \"integration_id\": \"<integer>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id/values", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id", "values"], "variable": [{"key": "project_id"}, {"key": "integration_id"}]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "text", "header": [], "cookie": []}, {"id": "c6e6f7ce-d622-4b6e-953e-7a1583b9c1e8", "name": "Bad Integration Extract Value params", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"value_source\": \"body\",\n  \"body_data_type\": \"json\",\n  \"key\": \"<string>\",\n  \"variable\": \"<string>\",\n  \"integration_id\": \"<integer>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id/values", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id", "values"], "variable": [{"key": "project_id"}, {"key": "integration_id"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "f13b63ad-1ac9-421b-af74-d434f6ec2229"}, {"name": "matchers", "item": [{"name": "{matcher_id}", "item": [{"name": "Updates Integration Matcher", "id": "5a5a6603-e57d-4069-b8dd-92a81587a48b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<string>\",\n  \"match_type\": \"header\",\n  \"method\": \"unequals\",\n  \"body_data_type\": \"string\",\n  \"key\": \"<string>\",\n  \"value\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id/matchers/:matcher_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id", "matchers", ":matcher_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "integration_id", "value": "<integer>", "description": "(Required) integration ID"}, {"key": "matcher_id", "value": "<integer>", "description": "(Required) matcher ID"}]}}, "response": [{"id": "1de952e3-9106-49aa-9d98-24726337831c", "name": "Integration Matcher updated", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<string>\",\n  \"match_type\": \"header\",\n  \"method\": \"unequals\",\n  \"body_data_type\": \"string\",\n  \"key\": \"<string>\",\n  \"value\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id/matchers/:matcher_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id", "matchers", ":matcher_id"], "variable": [{"key": "project_id"}, {"key": "integration_id"}, {"key": "matcher_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}, {"id": "8bd5c3dd-b348-4907-bf8b-05b45ff4be64", "name": "Bad integration matcher parameter", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<string>\",\n  \"match_type\": \"header\",\n  \"method\": \"unequals\",\n  \"body_data_type\": \"string\",\n  \"key\": \"<string>\",\n  \"value\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id/matchers/:matcher_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id", "matchers", ":matcher_id"], "variable": [{"key": "project_id"}, {"key": "integration_id"}, {"key": "matcher_id"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}, {"name": "Removes integration matcher", "id": "7e0f2c38-30f7-4143-aaac-6484f9471488", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id/matchers/:matcher_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id", "matchers", ":matcher_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "integration_id", "value": "<integer>", "description": "(Required) integration ID"}, {"key": "matcher_id", "value": "<integer>", "description": "(Required) matcher ID"}]}}, "response": [{"id": "31eb06ac-981e-4f60-8df3-f8db9920209b", "name": "integration matcher removed", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id/matchers/:matcher_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id", "matchers", ":matcher_id"], "variable": [{"key": "project_id"}, {"key": "integration_id"}, {"key": "matcher_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "eaed6943-5538-4a01-b11d-3578b07d22f9"}, {"name": "Get Integration Matcher linked to integration extractor", "id": "b56401dd-3f7c-4254-ba23-df7b3be86fba", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id/matchers", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id", "matchers"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "integration_id", "value": "<integer>", "description": "(Required) integration ID"}]}}, "response": [{"id": "4bc110c9-378e-4301-bea4-a844247976b4", "name": "Integration Matcher", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id/matchers", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id", "matchers"], "variable": [{"key": "project_id"}, {"key": "integration_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": \"<integer>\",\n    \"integration_id\": \"<integer>\",\n    \"name\": \"<string>\",\n    \"match_type\": \"header\",\n    \"method\": \"contains\",\n    \"body_data_type\": \"xml\",\n    \"key\": \"<string>\",\n    \"value\": \"<string>\"\n  },\n  {\n    \"id\": \"<integer>\",\n    \"integration_id\": \"<integer>\",\n    \"name\": \"<string>\",\n    \"match_type\": \"header\",\n    \"method\": \"contains\",\n    \"body_data_type\": \"string\",\n    \"key\": \"<string>\",\n    \"value\": \"<string>\"\n  }\n]"}]}, {"name": "Add Integration Matcher", "id": "5ca5997a-7a11-436b-9247-dcb9c673e5c0", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"integration_id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"match_type\": \"body\",\n  \"method\": \"unequals\",\n  \"body_data_type\": \"string\",\n  \"key\": \"<string>\",\n  \"value\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id/matchers", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id", "matchers"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "integration_id", "value": "<integer>", "description": "(Required) integration ID"}]}}, "response": [{"id": "4e1dfc52-7d79-4f61-a42f-ea4e6573d77d", "name": "Integration Matcher Created", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"integration_id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"match_type\": \"body\",\n  \"method\": \"unequals\",\n  \"body_data_type\": \"string\",\n  \"key\": \"<string>\",\n  \"value\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id/matchers", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id", "matchers"], "variable": [{"key": "project_id"}, {"key": "integration_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [], "cookie": []}, {"id": "36f62c44-29f8-4bf0-bd79-55d60c05db75", "name": "Bad Integration Matcher params", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"integration_id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"match_type\": \"body\",\n  \"method\": \"unequals\",\n  \"body_data_type\": \"string\",\n  \"key\": \"<string>\",\n  \"value\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id/matchers", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id", "matchers"], "variable": [{"key": "project_id"}, {"key": "integration_id"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "3c6158f6-91c7-4fc0-a482-f5c229a25b1b"}, {"name": "Update Integration", "id": "dfd20cf5-7786-414b-8b77-453def5d4f2a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"template_id\": \"<integer>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "integration_id", "value": "<integer>", "description": "(Required) integration ID"}]}}, "response": [{"id": "b39b6e3b-11b0-4395-8dea-3f59cb9731fd", "name": "Integration updated", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"template_id\": \"<integer>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id"], "variable": [{"key": "project_id"}, {"key": "integration_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}, {"name": "Remove integration", "id": "3eddc27b-b7ad-4637-9fb7-94a4b3d6c20d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "integration_id", "value": "<integer>", "description": "(Required) integration ID"}]}}, "response": [{"id": "312dcf9b-dfa2-4f40-99eb-b577202e13fb", "name": "integration removed", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/integrations/:integration_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations", ":integration_id"], "variable": [{"key": "project_id"}, {"key": "integration_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "957493da-989f-4e03-b3a0-5304f986484c"}, {"name": "get all integrations", "id": "b56e2c78-362f-4c1b-bf81-ef13810ccebf", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/integrations", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "b0b3686a-3546-4ea2-b61a-c546247869fd", "name": "integration", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/integrations", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations"], "variable": [{"key": "project_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": \"<integer>\",\n    \"name\": \"<string>\",\n    \"project_id\": \"<integer>\",\n    \"template_id\": \"<integer>\"\n  },\n  {\n    \"id\": \"<integer>\",\n    \"name\": \"<string>\",\n    \"project_id\": \"<integer>\",\n    \"template_id\": \"<integer>\"\n  }\n]"}]}, {"name": "create a new integration", "id": "668408ed-f5a3-4d28-b781-251757a1e8c1", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"template_id\": \"<integer>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/integrations", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "2247a74b-77f8-44ef-8641-f09deb85cbbc", "name": "Integration Created", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"template_id\": \"<integer>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/integrations", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "integrations"], "variable": [{"key": "project_id"}]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"template_id\": \"<integer>\"\n}"}]}], "id": "cc77f4b2-985f-47dd-b66b-2a193f624821"}, {"name": "keys", "item": [{"name": "{key_id}", "item": [{"name": "Updates access key", "id": "1c962787-570e-43a6-ba2e-4d1e6c0d275e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"type\": \"none\",\n  \"project_id\": \"<integer>\",\n  \"override_secret\": \"<boolean>\",\n  \"login_password\": {\n    \"password\": \"<string>\",\n    \"login\": \"<string>\"\n  },\n  \"ssh\": {\n    \"login\": \"<string>\",\n    \"passphrase\": \"<string>\",\n    \"private_key\": \"<string>\"\n  }\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/keys/:key_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "keys", ":key_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "key_id", "value": "<integer>", "description": "(Required) key ID"}]}}, "response": [{"id": "5efe88c0-5ff6-4efd-a4f2-659b2853775c", "name": "Key updated", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"type\": \"none\",\n  \"project_id\": \"<integer>\",\n  \"override_secret\": \"<boolean>\",\n  \"login_password\": {\n    \"password\": \"<string>\",\n    \"login\": \"<string>\"\n  },\n  \"ssh\": {\n    \"login\": \"<string>\",\n    \"passphrase\": \"<string>\",\n    \"private_key\": \"<string>\"\n  }\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/keys/:key_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "keys", ":key_id"], "variable": [{"key": "project_id"}, {"key": "key_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}, {"id": "1cf9aa9c-cb98-4f7c-8403-9f1f7a5567c3", "name": "Bad type", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"type\": \"none\",\n  \"project_id\": \"<integer>\",\n  \"override_secret\": \"<boolean>\",\n  \"login_password\": {\n    \"password\": \"<string>\",\n    \"login\": \"<string>\"\n  },\n  \"ssh\": {\n    \"login\": \"<string>\",\n    \"passphrase\": \"<string>\",\n    \"private_key\": \"<string>\"\n  }\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/keys/:key_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "keys", ":key_id"], "variable": [{"key": "project_id"}, {"key": "key_id"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}, {"name": "Removes access key", "id": "d2b3a57f-5d9c-49cf-8f15-8c6963c7c455", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/project/:project_id/keys/:key_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "keys", ":key_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "key_id", "value": "<integer>", "description": "(Required) key ID"}]}}, "response": [{"id": "30d1c47b-485b-4ec1-85b0-77473b2d685b", "name": "access key removed", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/keys/:key_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "keys", ":key_id"], "variable": [{"key": "project_id"}, {"key": "key_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "fab6ac75-0c50-44b3-95d0-5b9812d14e4d"}, {"name": "Get access keys linked to project", "id": "19a11020-22d0-4f44-b9ba-a1fab45a4011", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/keys?Key type=ssh&sort=name&order=asc", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "keys"], "query": [{"key": "Key type", "value": "ssh", "description": "Filter by key type"}, {"key": "sort", "value": "name", "description": "(Required) sorting name"}, {"key": "order", "value": "asc", "description": "(Required) ordering manner"}], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "4f2cef7f-81ae-409f-b9be-c50737a6a2bb", "name": "Access Keys", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/keys?Key type=ssh&sort=name&order=asc", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "keys"], "query": [{"description": "Filter by key type", "key": "Key type", "value": "ssh"}, {"description": "(Required) sorting name", "key": "sort", "value": "name"}, {"description": "(Required) ordering manner", "key": "order", "value": "asc"}], "variable": [{"key": "project_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": \"<integer>\",\n    \"name\": \"<string>\",\n    \"type\": \"ssh\",\n    \"project_id\": \"<integer>\"\n  },\n  {\n    \"id\": \"<integer>\",\n    \"name\": \"<string>\",\n    \"type\": \"none\",\n    \"project_id\": \"<integer>\"\n  }\n]"}]}, {"name": "Add access key", "id": "f4c4e359-0bd2-41ec-9624-2a9877f6cba3", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"type\": \"none\",\n  \"project_id\": \"<integer>\",\n  \"override_secret\": \"<boolean>\",\n  \"login_password\": {\n    \"password\": \"<string>\",\n    \"login\": \"<string>\"\n  },\n  \"ssh\": {\n    \"login\": \"<string>\",\n    \"passphrase\": \"<string>\",\n    \"private_key\": \"<string>\"\n  }\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/keys", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "keys"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "a6fc4ed8-aac4-4877-98ab-03c13f8e72e7", "name": "Access Key created", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"type\": \"none\",\n  \"project_id\": \"<integer>\",\n  \"override_secret\": \"<boolean>\",\n  \"login_password\": {\n    \"password\": \"<string>\",\n    \"login\": \"<string>\"\n  },\n  \"ssh\": {\n    \"login\": \"<string>\",\n    \"passphrase\": \"<string>\",\n    \"private_key\": \"<string>\"\n  }\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/keys", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "keys"], "variable": [{"key": "project_id"}]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"type\": \"ssh\",\n  \"project_id\": \"<integer>\"\n}"}, {"id": "59b6d52e-da84-44c9-851e-d4393afa9a46", "name": "Bad type", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"type\": \"none\",\n  \"project_id\": \"<integer>\",\n  \"override_secret\": \"<boolean>\",\n  \"login_password\": {\n    \"password\": \"<string>\",\n    \"login\": \"<string>\"\n  },\n  \"ssh\": {\n    \"login\": \"<string>\",\n    \"passphrase\": \"<string>\",\n    \"private_key\": \"<string>\"\n  }\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/keys", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "keys"], "variable": [{"key": "project_id"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "c880a7dd-6b66-40a7-adf2-d065646a2946"}, {"name": "repositories", "item": [{"name": "{repository_id}", "item": [{"name": "Get repository", "id": "872bcd9d-c4a9-4969-ae1c-910fd0c8ef1b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/repositories/:repository_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "repositories", ":repository_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "repository_id", "value": "<integer>", "description": "(Required) repository ID"}]}}, "response": [{"id": "90366237-fdd2-43a4-94ca-c069a339eaf4", "name": "repository object", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/repositories/:repository_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "repositories", ":repository_id"], "variable": [{"key": "project_id"}, {"key": "repository_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"git_url\": \"<string>\",\n  \"git_branch\": \"<string>\",\n  \"ssh_key_id\": \"<integer>\"\n}"}]}, {"name": "Updates repository", "id": "cd6dc4a3-e081-4e8e-90b7-f30c538a32a8", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"git_url\": \"<string>\",\n  \"git_branch\": \"<string>\",\n  \"ssh_key_id\": \"<integer>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/repositories/:repository_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "repositories", ":repository_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "repository_id", "value": "<integer>", "description": "(Required) repository ID"}]}}, "response": [{"id": "b6e72d6f-17b3-42fa-9982-ba4a235da1ba", "name": "Repository updated", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"git_url\": \"<string>\",\n  \"git_branch\": \"<string>\",\n  \"ssh_key_id\": \"<integer>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/repositories/:repository_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "repositories", ":repository_id"], "variable": [{"key": "project_id"}, {"key": "repository_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}, {"id": "be0a890a-de74-4e2d-ac29-c96031c6cc97", "name": "Bad request", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"git_url\": \"<string>\",\n  \"git_branch\": \"<string>\",\n  \"ssh_key_id\": \"<integer>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/repositories/:repository_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "repositories", ":repository_id"], "variable": [{"key": "project_id"}, {"key": "repository_id"}]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}, {"name": "Removes repository", "id": "9d84b566-30bd-41d0-b3b8-b3c<PERSON>ae5f7b", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/project/:project_id/repositories/:repository_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "repositories", ":repository_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "repository_id", "value": "<integer>", "description": "(Required) repository ID"}]}}, "response": [{"id": "d420f1b5-beeb-4299-bd2c-e250b485d09c", "name": "repository removed", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/repositories/:repository_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "repositories", ":repository_id"], "variable": [{"key": "project_id"}, {"key": "repository_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "327fb141-5c84-448f-bbaa-6b383df1b468"}, {"name": "Get repositories", "id": "53ca0d32-ac28-4715-9e2c-d17296d19564", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/repositories?sort=ssh_key&order=asc", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "repositories"], "query": [{"key": "sort", "value": "ssh_key", "description": "(Required) sorting name"}, {"key": "order", "value": "asc", "description": "(Required) ordering manner"}], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "42b8cdb1-d0b8-4d42-8540-41adef65476a", "name": "repositories", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/repositories?sort=ssh_key&order=asc", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "repositories"], "query": [{"description": "(Required) sorting name", "key": "sort", "value": "ssh_key"}, {"description": "(Required) ordering manner", "key": "order", "value": "asc"}], "variable": [{"key": "project_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": \"<integer>\",\n    \"name\": \"<string>\",\n    \"project_id\": \"<integer>\",\n    \"git_url\": \"<string>\",\n    \"git_branch\": \"<string>\",\n    \"ssh_key_id\": \"<integer>\"\n  },\n  {\n    \"id\": \"<integer>\",\n    \"name\": \"<string>\",\n    \"project_id\": \"<integer>\",\n    \"git_url\": \"<string>\",\n    \"git_branch\": \"<string>\",\n    \"ssh_key_id\": \"<integer>\"\n  }\n]"}]}, {"name": "Add repository", "id": "0c8f0684-42b5-4ea9-8ac1-8c37c7f71298", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"git_url\": \"<string>\",\n  \"git_branch\": \"<string>\",\n  \"ssh_key_id\": \"<integer>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/repositories", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "repositories"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "a493b80e-2207-4770-9be2-d34d602ee639", "name": "Repository created", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"git_url\": \"<string>\",\n  \"git_branch\": \"<string>\",\n  \"ssh_key_id\": \"<integer>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/repositories", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "repositories"], "variable": [{"key": "project_id"}]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"git_url\": \"<string>\",\n  \"git_branch\": \"<string>\",\n  \"ssh_key_id\": \"<integer>\"\n}"}]}], "id": "6d392179-addc-4252-9a34-daedcf6d09a0"}, {"name": "inventory", "item": [{"name": "{inventory_id}", "item": [{"name": "Get inventory", "id": "7d723843-fd62-48f4-a29a-fbeae54cee7d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/inventory/:inventory_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "inventory", ":inventory_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "inventory_id", "value": "<integer>", "description": "(Required) inventory ID"}]}}, "response": [{"id": "9f0a6546-326f-43cb-90f3-8807271704d0", "name": "inventory object", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/inventory/:inventory_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "inventory", ":inventory_id"], "variable": [{"key": "project_id"}, {"key": "inventory_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"inventory\": \"<string>\",\n  \"ssh_key_id\": \"<integer>\",\n  \"become_key_id\": \"<integer>\",\n  \"repository_id\": \"<integer>\",\n  \"type\": \"static\"\n}"}]}, {"name": "Updates inventory", "id": "709e9a94-8876-4d32-94cc-d600d21e3cf9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"inventory\": \"<string>\",\n  \"ssh_key_id\": \"<integer>\",\n  \"become_key_id\": \"<integer>\",\n  \"repository_id\": \"<integer>\",\n  \"type\": \"static\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/inventory/:inventory_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "inventory", ":inventory_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "inventory_id", "value": "<integer>", "description": "(Required) inventory ID"}]}}, "response": [{"id": "fcabbd06-a18f-4a1c-b69c-046173f60b0a", "name": "Inventory updated", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"inventory\": \"<string>\",\n  \"ssh_key_id\": \"<integer>\",\n  \"become_key_id\": \"<integer>\",\n  \"repository_id\": \"<integer>\",\n  \"type\": \"static\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/inventory/:inventory_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "inventory", ":inventory_id"], "variable": [{"key": "project_id"}, {"key": "inventory_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}, {"name": "Removes inventory", "id": "ee7ecd8a-5fd9-4aea-b54e-abcecd137a60", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/project/:project_id/inventory/:inventory_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "inventory", ":inventory_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "inventory_id", "value": "<integer>", "description": "(Required) inventory ID"}]}}, "response": [{"id": "c282c49b-a279-4c2f-80d6-398cfdca1cc8", "name": "inventory removed", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/inventory/:inventory_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "inventory", ":inventory_id"], "variable": [{"key": "project_id"}, {"key": "inventory_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "e34a0ff9-8cf0-41f6-8301-17b2ca43a702"}, {"name": "Get inventory", "id": "6f0c2597-d220-4e2f-ae84-089432c4dcb9", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/inventory?sort=name&order=asc", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "inventory"], "query": [{"key": "sort", "value": "name", "description": "(Required) sorting name"}, {"key": "order", "value": "asc", "description": "(Required) ordering manner"}], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "f26dd388-68b3-4ca5-8ddd-b120f35ef51f", "name": "inventory", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/inventory?sort=name&order=asc", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "inventory"], "query": [{"description": "(Required) sorting name", "key": "sort", "value": "name"}, {"description": "(Required) ordering manner", "key": "order", "value": "asc"}], "variable": [{"key": "project_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": \"<integer>\",\n    \"name\": \"<string>\",\n    \"project_id\": \"<integer>\",\n    \"inventory\": \"<string>\",\n    \"ssh_key_id\": \"<integer>\",\n    \"become_key_id\": \"<integer>\",\n    \"repository_id\": \"<integer>\",\n    \"type\": \"terraform-workspace\"\n  },\n  {\n    \"id\": \"<integer>\",\n    \"name\": \"<string>\",\n    \"project_id\": \"<integer>\",\n    \"inventory\": \"<string>\",\n    \"ssh_key_id\": \"<integer>\",\n    \"become_key_id\": \"<integer>\",\n    \"repository_id\": \"<integer>\",\n    \"type\": \"static-yaml\"\n  }\n]"}]}, {"name": "create inventory", "id": "56c4bcfc-069f-4182-ae99-7878b7aa0896", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"inventory\": \"<string>\",\n  \"ssh_key_id\": \"<integer>\",\n  \"become_key_id\": \"<integer>\",\n  \"repository_id\": \"<integer>\",\n  \"type\": \"static\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/inventory", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "inventory"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "170f0c0d-fd4a-48a7-8730-e0bb2789a661", "name": "inventory created", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"inventory\": \"<string>\",\n  \"ssh_key_id\": \"<integer>\",\n  \"become_key_id\": \"<integer>\",\n  \"repository_id\": \"<integer>\",\n  \"type\": \"static\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/inventory", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "inventory"], "variable": [{"key": "project_id"}]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"inventory\": \"<string>\",\n  \"ssh_key_id\": \"<integer>\",\n  \"become_key_id\": \"<integer>\",\n  \"repository_id\": \"<integer>\",\n  \"type\": \"static\"\n}"}]}], "id": "68b2c9da-f46e-4140-9e66-c4cb0960c938"}, {"name": "environment", "item": [{"name": "{environment_id}", "item": [{"name": "Get environment", "id": "bb14e4a5-1395-4a3c-bd60-f3d24ec85385", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/environment/:environment_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "environment", ":environment_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "environment_id", "value": "<integer>", "description": "(Required) environment ID"}]}}, "response": [{"id": "8e0eb9d5-48ec-4959-ab9c-167d9204e260", "name": "environment object", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/environment/:environment_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "environment", ":environment_id"], "variable": [{"key": "project_id"}, {"key": "environment_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"password\": \"<string>\",\n  \"json\": \"<string>\",\n  \"env\": \"<string>\",\n  \"secrets\": [\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"type\": \"env\"\n    },\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"type\": \"env\"\n    }\n  ]\n}"}]}, {"name": "Update environment", "id": "ee85eded-0bf2-4f2e-b8a0-6c36d7691823", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"password\": \"<string>\",\n  \"json\": \"<string>\",\n  \"env\": \"<string>\",\n  \"secrets\": [\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"secret\": \"<string>\",\n      \"type\": \"env\",\n      \"operation\": \"update\"\n    },\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"secret\": \"<string>\",\n      \"type\": \"var\",\n      \"operation\": \"create\"\n    }\n  ]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/environment/:environment_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "environment", ":environment_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "environment_id", "value": "<integer>", "description": "(Required) environment ID"}]}}, "response": [{"id": "c8d7184a-98fe-4988-ac25-34d367956c50", "name": "Environment Updated", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"password\": \"<string>\",\n  \"json\": \"<string>\",\n  \"env\": \"<string>\",\n  \"secrets\": [\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"secret\": \"<string>\",\n      \"type\": \"env\",\n      \"operation\": \"update\"\n    },\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"secret\": \"<string>\",\n      \"type\": \"var\",\n      \"operation\": \"create\"\n    }\n  ]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/environment/:environment_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "environment", ":environment_id"], "variable": [{"key": "project_id"}, {"key": "environment_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}, {"name": "Removes environment", "id": "98ceba89-88f4-49cb-a9ec-e162e0908045", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/project/:project_id/environment/:environment_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "environment", ":environment_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "environment_id", "value": "<integer>", "description": "(Required) environment ID"}]}}, "response": [{"id": "9aa29a4c-8c47-4509-884c-2a46ac8edf74", "name": "environment removed", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/environment/:environment_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "environment", ":environment_id"], "variable": [{"key": "project_id"}, {"key": "environment_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "0bf894f6-e139-4ff8-8dd1-55f4c17cd54d"}, {"name": "Get environment", "id": "b30a4604-8606-4f8b-8bd5-6ca9b424985f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/environment?sort=<string>&order=<string>", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "environment"], "query": [{"key": "sort", "value": "<string>", "description": "(Required) sorting name"}, {"key": "order", "value": "<string>", "description": "(Required) ordering manner"}], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "00eb66ba-8c4a-46d3-a2d5-da0e260619dd", "name": "environment", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/environment?sort=<string>&order=<string>", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "environment"], "query": [{"description": "(Required) sorting name", "key": "sort", "value": "<string>"}, {"description": "(Required) ordering manner", "key": "order", "value": "<string>"}], "variable": [{"key": "project_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": \"<integer>\",\n    \"name\": \"<string>\",\n    \"project_id\": \"<integer>\",\n    \"password\": \"<string>\",\n    \"json\": \"<string>\",\n    \"env\": \"<string>\",\n    \"secrets\": [\n      {\n        \"id\": \"<integer>\",\n        \"name\": \"<string>\",\n        \"type\": \"var\"\n      },\n      {\n        \"id\": \"<integer>\",\n        \"name\": \"<string>\",\n        \"type\": \"var\"\n      }\n    ]\n  },\n  {\n    \"id\": \"<integer>\",\n    \"name\": \"<string>\",\n    \"project_id\": \"<integer>\",\n    \"password\": \"<string>\",\n    \"json\": \"<string>\",\n    \"env\": \"<string>\",\n    \"secrets\": [\n      {\n        \"id\": \"<integer>\",\n        \"name\": \"<string>\",\n        \"type\": \"env\"\n      },\n      {\n        \"id\": \"<integer>\",\n        \"name\": \"<string>\",\n        \"type\": \"var\"\n      }\n    ]\n  }\n]"}]}, {"name": "Add environment", "id": "f4fa5c06-6af3-4b75-8ed5-2ef4d4e8e66e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"password\": \"<string>\",\n  \"json\": \"<string>\",\n  \"env\": \"<string>\",\n  \"secrets\": [\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"secret\": \"<string>\",\n      \"type\": \"env\",\n      \"operation\": \"update\"\n    },\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"secret\": \"<string>\",\n      \"type\": \"var\",\n      \"operation\": \"create\"\n    }\n  ]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/environment", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "environment"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "5e0a2865-0a27-4777-a8c0-86c0fce63a7b", "name": "Environment created", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"password\": \"<string>\",\n  \"json\": \"<string>\",\n  \"env\": \"<string>\",\n  \"secrets\": [\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"secret\": \"<string>\",\n      \"type\": \"env\",\n      \"operation\": \"update\"\n    },\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"secret\": \"<string>\",\n      \"type\": \"var\",\n      \"operation\": \"create\"\n    }\n  ]\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/environment", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "environment"], "variable": [{"key": "project_id"}]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"password\": \"<string>\",\n  \"json\": \"<string>\",\n  \"env\": \"<string>\",\n  \"secrets\": [\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"type\": \"env\"\n    },\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"type\": \"env\"\n    }\n  ]\n}"}]}], "id": "c5e31002-3b4f-47f7-b348-0ccee09582f7"}, {"name": "templates", "item": [{"name": "{template_id}", "item": [{"name": "Get template", "id": "104660cc-eda5-4562-ba18-657d17c1ad72", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/templates/:template_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "templates", ":template_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "template_id", "value": "<integer>", "description": "(Required) template ID"}]}}, "response": [{"id": "63c823fa-e752-472d-8ae8-5bc484f3c22f", "name": "template object", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/templates/:template_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "templates", ":template_id"], "variable": [{"key": "project_id"}, {"key": "template_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<integer>\",\n  \"project_id\": \"<integer>\",\n  \"inventory_id\": \"<integer>\",\n  \"repository_id\": \"<integer>\",\n  \"environment_id\": \"<integer>\",\n  \"view_id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"playbook\": \"<string>\",\n  \"arguments\": \"<string>\",\n  \"description\": \"<string>\",\n  \"allow_override_args_in_task\": \"<boolean>\",\n  \"suppress_success_alerts\": \"<boolean>\",\n  \"app\": \"<string>\",\n  \"git_branch\": \"<string>\",\n  \"type\": \"\",\n  \"autorun\": \"<boolean>\",\n  \"survey_vars\": [\n    {\n      \"name\": \"<string>\",\n      \"title\": \"<string>\",\n      \"description\": \"<string>\",\n      \"type\": \"enum\",\n      \"required\": \"<boolean>\",\n      \"values\": [\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        },\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        }\n      ]\n    },\n    {\n      \"name\": \"<string>\",\n      \"title\": \"<string>\",\n      \"description\": \"<string>\",\n      \"type\": \"int\",\n      \"required\": \"<boolean>\",\n      \"values\": [\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        },\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        }\n      ]\n    }\n  ],\n  \"vaults\": [\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"type\": \"script\"\n    },\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"type\": \"script\"\n    }\n  ]\n}"}]}, {"name": "Updates template", "id": "63b251a6-9c9f-4862-a367-96c33f832154", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"project_id\": \"<integer>\",\n  \"inventory_id\": \"<integer>\",\n  \"repository_id\": \"<integer>\",\n  \"environment_id\": \"<integer>\",\n  \"view_id\": \"<integer>\",\n  \"vaults\": [\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"type\": \"script\"\n    },\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"type\": \"script\"\n    }\n  ],\n  \"name\": \"<string>\",\n  \"playbook\": \"<string>\",\n  \"arguments\": \"<string>\",\n  \"description\": \"<string>\",\n  \"allow_override_args_in_task\": \"<boolean>\",\n  \"limit\": \"<string>\",\n  \"suppress_success_alerts\": \"<boolean>\",\n  \"app\": \"<string>\",\n  \"git_branch\": \"<string>\",\n  \"survey_vars\": [\n    {\n      \"name\": \"<string>\",\n      \"title\": \"<string>\",\n      \"description\": \"<string>\",\n      \"type\": \"\",\n      \"required\": \"<boolean>\",\n      \"values\": [\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        },\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        }\n      ]\n    },\n    {\n      \"name\": \"<string>\",\n      \"title\": \"<string>\",\n      \"description\": \"<string>\",\n      \"type\": \"int\",\n      \"required\": \"<boolean>\",\n      \"values\": [\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        },\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        }\n      ]\n    }\n  ],\n  \"type\": \"deploy\",\n  \"start_version\": \"<string>\",\n  \"build_template_id\": \"<integer>\",\n  \"autorun\": \"<boolean>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/templates/:template_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "templates", ":template_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "template_id", "value": "<integer>", "description": "(Required) template ID"}]}}, "response": [{"id": "2d3dc5b2-8443-43e9-a552-e0df9d827944", "name": "template updated", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"project_id\": \"<integer>\",\n  \"inventory_id\": \"<integer>\",\n  \"repository_id\": \"<integer>\",\n  \"environment_id\": \"<integer>\",\n  \"view_id\": \"<integer>\",\n  \"vaults\": [\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"type\": \"script\"\n    },\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"type\": \"script\"\n    }\n  ],\n  \"name\": \"<string>\",\n  \"playbook\": \"<string>\",\n  \"arguments\": \"<string>\",\n  \"description\": \"<string>\",\n  \"allow_override_args_in_task\": \"<boolean>\",\n  \"limit\": \"<string>\",\n  \"suppress_success_alerts\": \"<boolean>\",\n  \"app\": \"<string>\",\n  \"git_branch\": \"<string>\",\n  \"survey_vars\": [\n    {\n      \"name\": \"<string>\",\n      \"title\": \"<string>\",\n      \"description\": \"<string>\",\n      \"type\": \"\",\n      \"required\": \"<boolean>\",\n      \"values\": [\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        },\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        }\n      ]\n    },\n    {\n      \"name\": \"<string>\",\n      \"title\": \"<string>\",\n      \"description\": \"<string>\",\n      \"type\": \"int\",\n      \"required\": \"<boolean>\",\n      \"values\": [\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        },\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        }\n      ]\n    }\n  ],\n  \"type\": \"deploy\",\n  \"start_version\": \"<string>\",\n  \"build_template_id\": \"<integer>\",\n  \"autorun\": \"<boolean>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/templates/:template_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "templates", ":template_id"], "variable": [{"key": "project_id"}, {"key": "template_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}, {"name": "Removes template", "id": "31a2f18c-594d-487b-ac4e-88de98d7d94c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/project/:project_id/templates/:template_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "templates", ":template_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "template_id", "value": "<integer>", "description": "(Required) template ID"}]}}, "response": [{"id": "f0cac3e9-f1c9-42ba-9bfc-1409d707f935", "name": "template removed", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/templates/:template_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "templates", ":template_id"], "variable": [{"key": "project_id"}, {"key": "template_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "139525b7-c9fe-4fa1-b7eb-5b3d15aa21ea"}, {"name": "Get template", "id": "f4a3de02-10a4-4663-af97-95ed6bedae2f", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/templates?sort=ssh_key&order=asc", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "templates"], "query": [{"key": "sort", "value": "ssh_key", "description": "(Required) sorting name"}, {"key": "order", "value": "asc", "description": "(Required) ordering manner"}], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "0a0cd102-5c86-4b89-9b91-79128e81b042", "name": "template", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/templates?sort=ssh_key&order=asc", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "templates"], "query": [{"description": "(Required) sorting name", "key": "sort", "value": "ssh_key"}, {"description": "(Required) ordering manner", "key": "order", "value": "asc"}], "variable": [{"key": "project_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": \"<integer>\",\n    \"project_id\": \"<integer>\",\n    \"inventory_id\": \"<integer>\",\n    \"repository_id\": \"<integer>\",\n    \"environment_id\": \"<integer>\",\n    \"view_id\": \"<integer>\",\n    \"name\": \"<string>\",\n    \"playbook\": \"<string>\",\n    \"arguments\": \"<string>\",\n    \"description\": \"<string>\",\n    \"allow_override_args_in_task\": \"<boolean>\",\n    \"suppress_success_alerts\": \"<boolean>\",\n    \"app\": \"<string>\",\n    \"git_branch\": \"<string>\",\n    \"type\": \"\",\n    \"autorun\": \"<boolean>\",\n    \"survey_vars\": [\n      {\n        \"name\": \"<string>\",\n        \"title\": \"<string>\",\n        \"description\": \"<string>\",\n        \"type\": \"int\",\n        \"required\": \"<boolean>\",\n        \"values\": [\n          {\n            \"name\": \"<string>\",\n            \"value\": \"<string>\"\n          },\n          {\n            \"name\": \"<string>\",\n            \"value\": \"<string>\"\n          }\n        ]\n      },\n      {\n        \"name\": \"<string>\",\n        \"title\": \"<string>\",\n        \"description\": \"<string>\",\n        \"type\": \"secret\",\n        \"required\": \"<boolean>\",\n        \"values\": [\n          {\n            \"name\": \"<string>\",\n            \"value\": \"<string>\"\n          },\n          {\n            \"name\": \"<string>\",\n            \"value\": \"<string>\"\n          }\n        ]\n      }\n    ],\n    \"vaults\": [\n      {\n        \"id\": \"<integer>\",\n        \"name\": \"<string>\",\n        \"type\": \"script\"\n      },\n      {\n        \"id\": \"<integer>\",\n        \"name\": \"<string>\",\n        \"type\": \"script\"\n      }\n    ]\n  },\n  {\n    \"id\": \"<integer>\",\n    \"project_id\": \"<integer>\",\n    \"inventory_id\": \"<integer>\",\n    \"repository_id\": \"<integer>\",\n    \"environment_id\": \"<integer>\",\n    \"view_id\": \"<integer>\",\n    \"name\": \"<string>\",\n    \"playbook\": \"<string>\",\n    \"arguments\": \"<string>\",\n    \"description\": \"<string>\",\n    \"allow_override_args_in_task\": \"<boolean>\",\n    \"suppress_success_alerts\": \"<boolean>\",\n    \"app\": \"<string>\",\n    \"git_branch\": \"<string>\",\n    \"type\": \"deploy\",\n    \"autorun\": \"<boolean>\",\n    \"survey_vars\": [\n      {\n        \"name\": \"<string>\",\n        \"title\": \"<string>\",\n        \"description\": \"<string>\",\n        \"type\": \"secret\",\n        \"required\": \"<boolean>\",\n        \"values\": [\n          {\n            \"name\": \"<string>\",\n            \"value\": \"<string>\"\n          },\n          {\n            \"name\": \"<string>\",\n            \"value\": \"<string>\"\n          }\n        ]\n      },\n      {\n        \"name\": \"<string>\",\n        \"title\": \"<string>\",\n        \"description\": \"<string>\",\n        \"type\": \"\",\n        \"required\": \"<boolean>\",\n        \"values\": [\n          {\n            \"name\": \"<string>\",\n            \"value\": \"<string>\"\n          },\n          {\n            \"name\": \"<string>\",\n            \"value\": \"<string>\"\n          }\n        ]\n      }\n    ],\n    \"vaults\": [\n      {\n        \"id\": \"<integer>\",\n        \"name\": \"<string>\",\n        \"type\": \"script\"\n      },\n      {\n        \"id\": \"<integer>\",\n        \"name\": \"<string>\",\n        \"type\": \"password\"\n      }\n    ]\n  }\n]"}]}, {"name": "create template", "id": "7fa27285-92fe-48c2-bbb6-74730edf4fbb", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"project_id\": \"<integer>\",\n  \"inventory_id\": \"<integer>\",\n  \"repository_id\": \"<integer>\",\n  \"environment_id\": \"<integer>\",\n  \"view_id\": \"<integer>\",\n  \"vaults\": [\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"type\": \"script\"\n    },\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"type\": \"script\"\n    }\n  ],\n  \"name\": \"<string>\",\n  \"playbook\": \"<string>\",\n  \"arguments\": \"<string>\",\n  \"description\": \"<string>\",\n  \"allow_override_args_in_task\": \"<boolean>\",\n  \"limit\": \"<string>\",\n  \"suppress_success_alerts\": \"<boolean>\",\n  \"app\": \"<string>\",\n  \"git_branch\": \"<string>\",\n  \"survey_vars\": [\n    {\n      \"name\": \"<string>\",\n      \"title\": \"<string>\",\n      \"description\": \"<string>\",\n      \"type\": \"\",\n      \"required\": \"<boolean>\",\n      \"values\": [\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        },\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        }\n      ]\n    },\n    {\n      \"name\": \"<string>\",\n      \"title\": \"<string>\",\n      \"description\": \"<string>\",\n      \"type\": \"int\",\n      \"required\": \"<boolean>\",\n      \"values\": [\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        },\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        }\n      ]\n    }\n  ],\n  \"type\": \"deploy\",\n  \"start_version\": \"<string>\",\n  \"build_template_id\": \"<integer>\",\n  \"autorun\": \"<boolean>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/templates", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "templates"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "bf21b47e-88ad-4842-8adf-71a874f2b264", "name": "template created", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"project_id\": \"<integer>\",\n  \"inventory_id\": \"<integer>\",\n  \"repository_id\": \"<integer>\",\n  \"environment_id\": \"<integer>\",\n  \"view_id\": \"<integer>\",\n  \"vaults\": [\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"type\": \"script\"\n    },\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"type\": \"script\"\n    }\n  ],\n  \"name\": \"<string>\",\n  \"playbook\": \"<string>\",\n  \"arguments\": \"<string>\",\n  \"description\": \"<string>\",\n  \"allow_override_args_in_task\": \"<boolean>\",\n  \"limit\": \"<string>\",\n  \"suppress_success_alerts\": \"<boolean>\",\n  \"app\": \"<string>\",\n  \"git_branch\": \"<string>\",\n  \"survey_vars\": [\n    {\n      \"name\": \"<string>\",\n      \"title\": \"<string>\",\n      \"description\": \"<string>\",\n      \"type\": \"\",\n      \"required\": \"<boolean>\",\n      \"values\": [\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        },\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        }\n      ]\n    },\n    {\n      \"name\": \"<string>\",\n      \"title\": \"<string>\",\n      \"description\": \"<string>\",\n      \"type\": \"int\",\n      \"required\": \"<boolean>\",\n      \"values\": [\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        },\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        }\n      ]\n    }\n  ],\n  \"type\": \"deploy\",\n  \"start_version\": \"<string>\",\n  \"build_template_id\": \"<integer>\",\n  \"autorun\": \"<boolean>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/templates", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "templates"], "variable": [{"key": "project_id"}]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<integer>\",\n  \"project_id\": \"<integer>\",\n  \"inventory_id\": \"<integer>\",\n  \"repository_id\": \"<integer>\",\n  \"environment_id\": \"<integer>\",\n  \"view_id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"playbook\": \"<string>\",\n  \"arguments\": \"<string>\",\n  \"description\": \"<string>\",\n  \"allow_override_args_in_task\": \"<boolean>\",\n  \"suppress_success_alerts\": \"<boolean>\",\n  \"app\": \"<string>\",\n  \"git_branch\": \"<string>\",\n  \"type\": \"\",\n  \"autorun\": \"<boolean>\",\n  \"survey_vars\": [\n    {\n      \"name\": \"<string>\",\n      \"title\": \"<string>\",\n      \"description\": \"<string>\",\n      \"type\": \"enum\",\n      \"required\": \"<boolean>\",\n      \"values\": [\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        },\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        }\n      ]\n    },\n    {\n      \"name\": \"<string>\",\n      \"title\": \"<string>\",\n      \"description\": \"<string>\",\n      \"type\": \"int\",\n      \"required\": \"<boolean>\",\n      \"values\": [\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        },\n        {\n          \"name\": \"<string>\",\n          \"value\": \"<string>\"\n        }\n      ]\n    }\n  ],\n  \"vaults\": [\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"type\": \"script\"\n    },\n    {\n      \"id\": \"<integer>\",\n      \"name\": \"<string>\",\n      \"type\": \"script\"\n    }\n  ]\n}"}]}], "id": "68b7154b-7174-420c-8659-d198191455fa"}, {"name": "schedules", "item": [{"name": "{schedule_id}", "item": [{"name": "Get schedule", "id": "65c16c30-9d22-4410-846f-debceac2b745", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/schedules/:schedule_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "schedules", ":schedule_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "schedule_id", "value": "<integer>", "description": "(Required) schedule ID"}]}}, "response": [{"id": "0d37ff14-9e52-4668-8c2a-0ef7243abdd2", "name": "Schedule", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/schedules/:schedule_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "schedules", ":schedule_id"], "variable": [{"key": "project_id"}, {"key": "schedule_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<integer>\",\n  \"cron_format\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"template_id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"active\": \"<boolean>\"\n}"}]}, {"name": "Updates schedule", "id": "83df279b-afd4-4dce-bc75-d08b29ae42eb", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"cron_format\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"template_id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"active\": \"<boolean>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/schedules/:schedule_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "schedules", ":schedule_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "schedule_id", "value": "<integer>", "description": "(Required) schedule ID"}]}}, "response": [{"id": "9264c4a4-db34-4933-afa5-e024f7f7cb2e", "name": "schedule updated", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"cron_format\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"template_id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"active\": \"<boolean>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/schedules/:schedule_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "schedules", ":schedule_id"], "variable": [{"key": "project_id"}, {"key": "schedule_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}, {"name": "Deletes schedule", "id": "b075b5c5-0a22-4041-b372-c7a3774575a3", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/project/:project_id/schedules/:schedule_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "schedules", ":schedule_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "schedule_id", "value": "<integer>", "description": "(Required) schedule ID"}]}}, "response": [{"id": "7d206099-71b9-4644-a3ba-0e1fc79c0a8a", "name": "schedule deleted", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/schedules/:schedule_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "schedules", ":schedule_id"], "variable": [{"key": "project_id"}, {"key": "schedule_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "fd3ae42c-4cd6-43d5-a2c6-cf62f55e504f"}, {"name": "create schedule", "id": "4b2e5541-b4b1-4bf8-bc3c-051e98456ea8", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"cron_format\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"template_id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"active\": \"<boolean>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/schedules", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "schedules"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "a2c71909-8d45-4c52-ad26-c0a52f903205", "name": "schedule created", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"id\": \"<integer>\",\n  \"cron_format\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"template_id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"active\": \"<boolean>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/schedules", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "schedules"], "variable": [{"key": "project_id"}]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<integer>\",\n  \"cron_format\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"template_id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"active\": \"<boolean>\"\n}"}]}], "id": "0e1ab1e1-0a47-4016-9381-208f69f3aac3"}, {"name": "views", "item": [{"name": "{view_id}", "item": [{"name": "Get view", "id": "d3340dc1-7c34-4fc1-b9e8-620deb2176d7", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/views/:view_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "views", ":view_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "view_id", "value": "<integer>", "description": "(Required) view ID"}]}}, "response": [{"id": "89a245aa-8f3f-4fa0-b428-736d21d44b08", "name": "view object", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/views/:view_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "views", ":view_id"], "variable": [{"key": "project_id"}, {"key": "view_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<integer>\",\n  \"title\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"position\": \"<integer>\"\n}"}]}, {"name": "Updates view", "id": "dd0e4697-0753-4c35-be75-9e3afc004381", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"position\": \"<integer>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/views/:view_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "views", ":view_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "view_id", "value": "<integer>", "description": "(Required) view ID"}]}}, "response": [{"id": "fbaf6219-0ddf-49c6-a755-05e4381120ad", "name": "view updated", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"position\": \"<integer>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/views/:view_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "views", ":view_id"], "variable": [{"key": "project_id"}, {"key": "view_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}, {"name": "Removes view", "id": "e6495c34-79a7-4809-ada5-ed5b668a0458", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/project/:project_id/views/:view_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "views", ":view_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "view_id", "value": "<integer>", "description": "(Required) view ID"}]}}, "response": [{"id": "ad5a11cb-56db-4969-aa54-adca4cc8208f", "name": "view removed", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/views/:view_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "views", ":view_id"], "variable": [{"key": "project_id"}, {"key": "view_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "cfa391bb-784b-4ba1-8df5-1350918bf061"}, {"name": "Get view", "id": "29ca97a9-c34d-41df-ab09-067e78b4c257", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/views", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "views"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "15243995-542e-4963-a403-6f9c276c2bdf", "name": "view", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/views", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "views"], "variable": [{"key": "project_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": \"<integer>\",\n    \"title\": \"<string>\",\n    \"project_id\": \"<integer>\",\n    \"position\": \"<integer>\"\n  },\n  {\n    \"id\": \"<integer>\",\n    \"title\": \"<string>\",\n    \"project_id\": \"<integer>\",\n    \"position\": \"<integer>\"\n  }\n]"}]}, {"name": "create view", "id": "61d2d149-2936-4274-a1a2-26b835c57839", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"position\": \"<integer>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/views", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "views"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "3a6133df-7c5f-4e39-8315-18a3d5229e34", "name": "view created", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"position\": \"<integer>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/views", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "views"], "variable": [{"key": "project_id"}]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<integer>\",\n  \"title\": \"<string>\",\n  \"project_id\": \"<integer>\",\n  \"position\": \"<integer>\"\n}"}]}], "id": "5af42808-2c2a-4a54-94d2-4a537c140f0a"}, {"name": "tasks", "item": [{"name": "last", "item": [{"name": "Get last 200 Tasks related to current project", "id": "39af373b-f117-48ef-9adc-fc164dd8982a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/tasks/last", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "tasks", "last"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "4ba7fd9f-7ce6-4533-9ff6-251c9489dfa3", "name": "Array of tasks in chronological order", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/tasks/last", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "tasks", "last"], "variable": [{"key": "project_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": \"<integer>\",\n    \"template_id\": \"<integer>\",\n    \"status\": \"<string>\",\n    \"debug\": \"<boolean>\",\n    \"playbook\": \"<string>\",\n    \"environment\": \"<string>\",\n    \"secret\": \"<string>\",\n    \"limit\": \"<string>\",\n    \"message\": \"<string>\"\n  },\n  {\n    \"id\": \"<integer>\",\n    \"template_id\": \"<integer>\",\n    \"status\": \"<string>\",\n    \"debug\": \"<boolean>\",\n    \"playbook\": \"<string>\",\n    \"environment\": \"<string>\",\n    \"secret\": \"<string>\",\n    \"limit\": \"<string>\",\n    \"message\": \"<string>\"\n  }\n]"}]}], "id": "659b921d-20e1-4ebf-a31d-932a2bd0424e"}, {"name": "{task_id}", "item": [{"name": "stop", "item": [{"name": "Stop a job", "id": "cb2d3afc-c82d-48c9-a7f3-148bed58547a", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/project/:project_id/tasks/:task_id/stop", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "tasks", ":task_id", "stop"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "task_id", "value": "<integer>", "description": "(Required) task ID"}]}}, "response": [{"id": "9e9dbcc6-2252-4284-a9b6-baa0cc13a8ba", "name": "Task queued", "originalRequest": {"method": "POST", "header": [{"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/tasks/:task_id/stop", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "tasks", ":task_id", "stop"], "variable": [{"key": "project_id"}, {"key": "task_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "8dad53c7-e78b-48c3-a33f-895f642c4f62"}, {"name": "output", "item": [{"name": "Get task output", "id": "8df8642f-0e71-42d5-8ff3-6d45f6e88c6c", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/tasks/:task_id/output", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "tasks", ":task_id", "output"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "task_id", "value": "<integer>", "description": "(Required) task ID"}]}}, "response": [{"id": "cc08bfb5-bd10-4c18-940f-2194c9567d07", "name": "output", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/tasks/:task_id/output", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "tasks", ":task_id", "output"], "variable": [{"key": "project_id"}, {"key": "task_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"task_id\": \"<integer>\",\n    \"time\": \"<dateTime>\",\n    \"output\": \"<string>\"\n  },\n  {\n    \"task_id\": \"<integer>\",\n    \"time\": \"<dateTime>\",\n    \"output\": \"<string>\"\n  }\n]"}]}], "id": "3b429255-22f0-4e3b-8280-ac4a32805cbd"}, {"name": "raw_output", "item": [{"name": "Get task raw output", "id": "328dca7d-e259-49aa-8b71-f39515b09e15", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/project/:project_id/tasks/:task_id/raw_output", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "tasks", ":task_id", "raw_output"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "task_id", "value": "<integer>", "description": "(Required) task ID"}]}}, "response": [{"id": "9f6cd61a-17f9-43e3-b08d-2780c4873926", "name": "output", "originalRequest": {"method": "GET", "header": [{"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/tasks/:task_id/raw_output", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "tasks", ":task_id", "raw_output"], "variable": [{"key": "project_id"}, {"key": "task_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"disabled": false, "description": {"content": "", "type": "text/plain"}, "key": "content-type", "value": "<string>"}], "cookie": []}]}], "id": "890592eb-ee91-434c-8f96-5c1900964ad2"}, {"name": "Get a single task", "id": "0d0dcc7b-60e7-4328-8768-b88d600230eb", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/tasks/:task_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "tasks", ":task_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "task_id", "value": "<integer>", "description": "(Required) task ID"}]}}, "response": [{"id": "ad5d835b-71b8-4993-8759-34b8d1ee456d", "name": "Task", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/tasks/:task_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "tasks", ":task_id"], "variable": [{"key": "project_id"}, {"key": "task_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<integer>\",\n  \"template_id\": \"<integer>\",\n  \"status\": \"<string>\",\n  \"debug\": \"<boolean>\",\n  \"playbook\": \"<string>\",\n  \"environment\": \"<string>\",\n  \"secret\": \"<string>\",\n  \"limit\": \"<string>\",\n  \"message\": \"<string>\"\n}"}]}, {"name": "Deletes task (including output)", "id": "36b22ee0-aee3-4006-9ad6-774bcd54475e", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/project/:project_id/tasks/:task_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "tasks", ":task_id"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}, {"key": "task_id", "value": "<integer>", "description": "(Required) task ID"}]}}, "response": [{"id": "37a671c8-36c4-4ee1-b90e-be545ee45270", "name": "task deleted", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/tasks/:task_id", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "tasks", ":task_id"], "variable": [{"key": "project_id"}, {"key": "task_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "a2742d7d-b84b-4d89-8bd8-f60140d15e3b"}, {"name": "Get Tasks related to current project", "id": "53013abf-22ff-45d3-a694-79a0ab5932fe", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/tasks", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "tasks"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "c0581133-5871-42f6-81c0-fe3b70df095e", "name": "Array of tasks in chronological order", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/tasks", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "tasks"], "variable": [{"key": "project_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[\n  {\n    \"id\": \"<integer>\",\n    \"template_id\": \"<integer>\",\n    \"status\": \"<string>\",\n    \"debug\": \"<boolean>\",\n    \"playbook\": \"<string>\",\n    \"environment\": \"<string>\",\n    \"secret\": \"<string>\",\n    \"limit\": \"<string>\",\n    \"message\": \"<string>\"\n  },\n  {\n    \"id\": \"<integer>\",\n    \"template_id\": \"<integer>\",\n    \"status\": \"<string>\",\n    \"debug\": \"<boolean>\",\n    \"playbook\": \"<string>\",\n    \"environment\": \"<string>\",\n    \"secret\": \"<string>\",\n    \"limit\": \"<string>\",\n    \"message\": \"<string>\"\n  }\n]"}]}, {"name": "Starts a job", "id": "fd7ac7eb-1a40-497e-8526-c4c43b8e5be8", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"template_id\": \"<integer>\",\n  \"debug\": \"<boolean>\",\n  \"dry_run\": \"<boolean>\",\n  \"diff\": \"<boolean>\",\n  \"playbook\": \"<string>\",\n  \"environment\": \"<string>\",\n  \"limit\": \"<string>\",\n  \"git_branch\": \"<string>\",\n  \"message\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/tasks", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "tasks"], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "10c09cae-bebc-4b76-b187-a081e5edef44", "name": "Task queued", "originalRequest": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"template_id\": \"<integer>\",\n  \"debug\": \"<boolean>\",\n  \"dry_run\": \"<boolean>\",\n  \"diff\": \"<boolean>\",\n  \"playbook\": \"<string>\",\n  \"environment\": \"<string>\",\n  \"limit\": \"<string>\",\n  \"git_branch\": \"<string>\",\n  \"message\": \"<string>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/tasks", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", "tasks"], "variable": [{"key": "project_id"}]}}, "status": "Created", "code": 201, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<integer>\",\n  \"template_id\": \"<integer>\",\n  \"status\": \"<string>\",\n  \"debug\": \"<boolean>\",\n  \"playbook\": \"<string>\",\n  \"environment\": \"<string>\",\n  \"secret\": \"<string>\",\n  \"limit\": \"<string>\",\n  \"message\": \"<string>\"\n}"}]}], "id": "24236c3c-5fc9-4524-9502-a96042d391a5"}, {"name": "Fetch project", "id": "6c6b380d-104e-422a-841d-5c9aab5ab00d", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/project/:project_id/", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", ""], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "7b9968a9-8ab3-4f69-84fa-e1ecacaeefcc", "name": "Project", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", ""], "variable": [{"key": "project_id"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"id\": \"<integer>\",\n  \"name\": \"<string>\",\n  \"created\": \"<string>\",\n  \"alert\": \"<boolean>\",\n  \"max_parallel_tasks\": \"<integer>\"\n}"}]}, {"name": "Update project", "id": "064758e8-1c30-47bf-9168-c5752e6c9ae5", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<string>\",\n  \"alert\": \"<boolean>\",\n  \"max_parallel_tasks\": \"<integer>\",\n  \"demo\": \"<boolean>\",\n  \"id\": \"<integer>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", ""], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "0e61e34d-a322-481a-9b4a-390a6059671f", "name": "Project saved", "originalRequest": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<string>\",\n  \"alert\": \"<boolean>\",\n  \"max_parallel_tasks\": \"<integer>\",\n  \"demo\": \"<boolean>\",\n  \"id\": \"<integer>\"\n}", "options": {"raw": {"headerFamily": "json", "language": "json"}}}, "url": {"raw": "{{baseUrl}}/project/:project_id/", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", ""], "variable": [{"key": "project_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}, {"name": "Delete project", "id": "bb3f76a1-0ed3-49de-8098-3c12b65d7d34", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/project/:project_id/", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", ""], "variable": [{"key": "project_id", "value": "<integer>", "description": "(Required) Project ID"}]}}, "response": [{"id": "cf929784-e50f-42d5-9990-28f3b9a5168e", "name": "Project deleted", "originalRequest": {"method": "DELETE", "header": [{"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/project/:project_id/", "host": ["{{baseUrl}}"], "path": ["project", ":project_id", ""], "variable": [{"key": "project_id"}]}}, "status": "No Content", "code": 204, "_postman_previewlanguage": "text", "header": [], "cookie": []}]}], "id": "e62e02fc-6769-4246-ac10-4edc9bcdc04d"}], "id": "27661c38-165d-4b9b-91d0-1885b8130bfc"}, {"name": "apps", "item": [{"name": "Get apps", "id": "6664f5e2-42f6-45a3-9b8a-69a751c94922", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/apps", "host": ["{{baseUrl}}"], "path": ["apps"]}}, "response": [{"id": "f6fb260d-d723-4803-87f8-fed63798520f", "name": "Apps", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"description": "Added as a part of security scheme: apikey", "key": "Authorization", "value": "<API Key>"}], "url": {"raw": "{{baseUrl}}/apps", "host": ["{{baseUrl}}"], "path": ["apps"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "[]"}]}], "id": "a972b945-e9f9-40e6-b26a-1d5d056a0cac"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{a<PERSON><PERSON><PERSON>}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"id": "50738f66-a31b-4749-8930-0bc96ba6c2ae", "type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"id": "88b8e162-2675-4bda-8bc9-245a51b2ad29", "type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"id": "ccc1b51e-356a-429b-aaa8-082bc2487fad", "key": "baseUrl", "value": "http://localhost:3000/api"}]}