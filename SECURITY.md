# Security Policy

## Supported Versions


| Version | Supported          |
| ------- | ------------------ |
| 2.14.x  | :white_check_mark: |
| 2.13.x  | :white_check_mark: |
| < 2.13  | :x:                |

## Reporting a Vulnerability

If you believe you’ve found a security vulnerability in Semaphore UI, we encourage you to let us know as soon as possible.

Please email <NAME_EMAIL> with:

- A clear description of the vulnerability
- Steps to reproduce the issue
- Any related logs, screenshots, or payloads

We take security seriously and will respond as quickly as possible. We aim to confirm receipt within 1 business day and provide a full response within 7 business days.

We ask that you **do not publicly disclose** the issue until we’ve had a chance to investigate and release a fix.

## Scope

This policy applies to:

- Semaphore UI (self-hosted)
- Official installers, containers, and packages distributed through our GitHub or website

This policy does **not** apply to third-party plugins or custom modifications.
