# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY
configVersion = 1.1.0
type = apiEntityData

[config]
id = 4023cf7c-aabb-4d5a-a742-72dadbd4924a

[config.relations]

[config.relations.collections]
rootDirectory = .postman/collections
files[] = {"id":"2979975-3e2a871a-8dc1-4771-b62a-45f68caa2b1b","path":"Semaphore API Documentation.json","metaData":{"generateCollectionPreferences":"{\"requestNameSource\":\"Fallback\",\"indentCharacter\":\"Space\",\"parametersResolution\":\"Schema\",\"folderStrategy\":\"Paths\",\"includeAuthInfoInExample\":true,\"enableOptionalParameters\":true,\"keepImplicitHeaders\":false,\"includeDeprecated\":true,\"alwaysInheritAuthentication\":false,\"updateCollectionSync\":true,\"requestParametersResolution\":\"Schema\",\"exampleParametersResolution\":\"Schema\"}"}}

[config.relations.collections.metaData]

[config.relations.apiDefinition]
files[] = {"path":"openapi.yml","metaData":{}}

[config.relations.apiDefinition.metaData]
type = openapi:3
rootFiles[] = openapi.yml
