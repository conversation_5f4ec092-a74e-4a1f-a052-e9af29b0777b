{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch Server",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "./cli/main.go",
            "args": ["server", "--config", "${workspaceFolder}/.devcontainer/config.json"],
            "cwd": "${workspaceFolder}",
            "env": {
                "PATH": "${workspaceFolder}/.venv/bin:${env:PATH}",
                "SEMAPHORE_ADMIN_PASSWORD": "test123"
            }
        }, 
        {
            "name": "Launch Server with remote Runner",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "./cli/main.go",
            "args": ["server", "--config", "${workspaceFolder}/.devcontainer/config.json"],
            "cwd": "${workspaceFolder}",
            "env": {
                "PATH": "${workspaceFolder}/.venv/bin:${env:PATH}",
                "SEMAPHORE_USE_REMOTE_RUNNER": "true"
            }
        }, 
        {
            "name": "Launch Runner",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "./cli/main.go",
            "args": ["runner", "start", "--config", "${workspaceFolder}/.devcontainer/config-runner.json", "--log-level", "debug"],
            "cwd": "${workspaceFolder}",
            "env": {
                "PATH": "${workspaceFolder}/.venv/bin:${env:PATH}"
            }
        }, 
        {
            "name": "Launch Dredd Tests without Server",
            "type": "pwa-node",
            "request": "launch",
            "runtimeExecutable": "task",
            "args": ["dredd:test:local"],
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal"
        }, 
        {
            "name": "Launch Server (LOCAL)",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "./cli/main.go",
            "args": ["server", "--config", "./config.json"],
            "cwd": "${workspaceFolder}",
        }, 
    ]
}