#!/bin/sh

go install github.com/go-task/task/v3/cmd/task@latest

(cd ./web && npm install)

python3 -m venv .venv

./.venv/bin/pip3 install ansible

task build
task dredd:goodman
task dredd:hooks

cp ./.devcontainer/config.json ./.dredd/config.json

./bin/semaphore user add \
    --admin \
    --login admin \
    --name Admin \
    --email <EMAIL> \
    --password changeme \
    --config ./.devcontainer/config.json