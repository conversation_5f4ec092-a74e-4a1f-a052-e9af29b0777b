#!/bin/sh
set -e
echoerr() { printf "%s\n" "$*" >&2; }

export SEMAPHORE_CONFIG_PATH="${SEMAPHORE_CONFIG_PATH:-/etc/semaphore}"
export SEMAPHORE_TMP_PATH="${SEMAPHORE_TMP_PATH:-/tmp/semaphore}"

if test -f "${SEMAPHORE_CONFIG_PATH}/requirements.txt"; then
    echoerr "Installing additional python dependencies"
    pip3 install --upgrade \
        -r "${SEMAPHORE_CONFIG_PATH}/requirements.txt"
else
    echoerr "No additional python dependencies to install"
fi

echoerr "Starting semaphore runner"
if test "$#" -ne 1; then
    exec /usr/local/bin/semaphore runner start --no-config
else
    exec "$@"
fi
