version: "3.4"

volumes:
  postgres:

services:
  server:
    environment:
      SEMAPHORE_DB_DIALECT: postgres
      SEMAPHORE_DB_HOST: db
      SEMAPHORE_DB_PORT: 5432
      SEMAPHORE_DB_USER: ${POSTGRES_USERNAME:-semaphore}
      SEMAPHORE_DB_PASS: ${POSTGRES_PASSWORD:-semaphore}
      SEMAPHORE_DB: ${POSTGRES_DATABASE:-semaphore}
    depends_on:
      - db

  db:
    image: postgres:14.3
    restart: always
    environment:
      POSTGRES_USER: ${POSTGRES_USERNAME:-semaphore}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-semaphore}
      POSTGRES_DB: ${POSTGRES_DATABASE:-semaphore}
    volumes:
      - postgres:/var/lib/postgresql
