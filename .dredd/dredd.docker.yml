dry-run: null
hookfiles: ./.dredd/compiled_hooks
language: go
server-wait: 5
init: false
custom: {}
names: false
only: []
reporter: []
output: []
header: "Authorization: bearer h4a_i4qslpnxyyref71rk5nqbwxccrs7enwvggx0vfs="
sorted: false
user: null
inline-errors: false
details: false
method: []
color: true
loglevel: debug
path: []
hooks-worker-timeout: 5000
hooks-worker-connect-timeout: 1500
hooks-worker-connect-retry: 500
hooks-worker-after-connect-wait: 100
hooks-worker-term-timeout: 5000
hooks-worker-term-retry: 500
hooks-worker-handler-host: 0.0.0.0
hooks-worker-handler-port: 61321
config: ./.dredd/dredd.yml
blueprint: api-docs.yml
endpoint: 'http://server:3000'
