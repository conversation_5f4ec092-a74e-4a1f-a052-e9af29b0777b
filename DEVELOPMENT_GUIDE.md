# Semaphore 开发环境启动和调试指南

## 项目概述

Semaphore 是一个基于 Go + Vue.js 的 Ansible UI 管理工具，包含：
- **后端**: Go 语言编写的 API 服务器
- **前端**: Vue.js + Vuetify 的 Web 界面
- **数据库**: BoltDB (嵌入式数据库)

## 目录结构

```
semaphore/
├── bin/                    # 编译后的二进制文件
│   └── semaphore          # 主程序
├── web/                   # 前端项目
│   ├── src/              # Vue.js 源码
│   ├── package.json      # Node.js 依赖
│   └── vue.config.js     # Vue 配置文件
├── config.json           # 后端配置文件
├── database.boltdb       # BoltDB 数据库文件
└── README.md
```

## 环境要求

### 后端要求
- Go 1.19+ (如果需要编译源码)
- 已编译的 `bin/semaphore` 二进制文件

### 前端要求
- Node.js 14+
- npm 或 yarn

## 快速启动

### 1. 启动后端 API 服务器

```bash
# 进入项目根目录
cd /path/to/semaphore

# 启动 API 服务器
./bin/semaphore server --config config.json
```

**启动成功标志:**
```
Loading config
Validating config
BoltDB ./database.boltdb
Tmp Path (projects home) /tmp/semaphore
Semaphore develop-daea1fdd-1745905758
Interface
Port :3000
Server is running
```

**API 服务器信息:**
- 端口: 3000
- 健康检查: http://localhost:3000/api/ping
- Web 界面: http://localhost:3000 (生产模式)

### 2. 启动前端开发服务器

```bash
# 进入前端目录
cd web

# 安装依赖 (首次运行)
npm install

# 启动开发服务器
npm run serve
```

**启动成功标志:**
```
App running at:
- Local:   http://localhost:8080/
- Network: http://192.168.x.x:8080/
```

**前端开发服务器信息:**
- 端口: 8080
- 热重载: 支持
- API 代理: 自动代理 `/api/*` 到 `http://localhost:3000`

## 详细启动步骤

### 后端启动详解

#### 1. 检查配置文件
```bash
# 查看当前配置
cat config.json
```

**重要配置项:**
```json
{
  "bolt": {
    "host": "./database.boltdb"  // 数据库文件路径
  },
  "dialect": "bolt",             // 数据库类型
  "tmp_path": "/tmp/semaphore",  // 临时文件路径
  "cookie_hash": "...",          // Cookie 加密密钥
  "cookie_encryption": "...",    // Cookie 加密密钥
  "access_key_encryption": "..." // 访问密钥加密
}
```

#### 2. 检查数据库文件
```bash
# 检查数据库文件是否存在
ls -la database.boltdb

# 如果不存在，需要运行初始化
./bin/semaphore setup
```

#### 3. 启动选项
```bash
# 基本启动
./bin/semaphore server --config config.json

# 指定日志级别
./bin/semaphore server --config config.json --log-level DEBUG

# 查看所有选项
./bin/semaphore server --help
```

#### 4. 常见启动问题

**问题 1: 数据库文件路径错误**
```
panic: open /IdeaProjects/semaphore/database.boltdb: no such file or directory
```
**解决方案:** 修改 `config.json` 中的数据库路径为相对路径 `./database.boltdb`

**问题 2: 端口被占用**
```
listen tcp :3000: bind: address already in use
```
**解决方案:**
- 杀死占用端口的进程: `lsof -ti:3000 | xargs kill -9`
- 或修改配置文件中的端口

### 前端启动详解

#### 1. 安装依赖
```bash
cd web

# 使用 npm
npm install

# 或使用 yarn
yarn install
```

#### 2. 开发模式启动
```bash
# 启动开发服务器
npm run serve

# 指定端口
npm run serve -- --port 8081
```

#### 3. 生产模式构建
```bash
# 构建生产版本
npm run build

# 构建后的文件会输出到 ../api/public/
```

#### 4. 前端配置说明

**vue.config.js 关键配置:**
```javascript
module.exports = {
  configureWebpack: {
    devServer: {
      proxy: {
        '^/api': {
          target: 'http://localhost:3000',  // API 代理目标
        },
      },
    },
  },
  outputDir: '../api/public',  // 生产构建输出目录
};
```

## 调试指南

### 后端调试

#### 1. 日志调试
```bash
# 启用 DEBUG 日志
./bin/semaphore server --config config.json --log-level DEBUG
```

#### 2. API 测试
```bash
# 测试 API 连通性
curl http://localhost:3000/api/ping

# 测试用户信息 (需要登录)
curl -H "Content-Type: application/json" \
     -X GET http://localhost:3000/api/user

# 测试登录
curl -H "Content-Type: application/json" \
     -X POST http://localhost:3000/api/auth/login \
     -d '{"auth":"admin","password":"admin"}'
```

#### 3. 数据库调试
```bash
# 查看数据库文件大小
ls -lh database.boltdb

# 备份数据库
cp database.boltdb database.boltdb.backup
```

### 前端调试

#### 1. 浏览器开发者工具
- **F12** 打开开发者工具
- **Network** 标签查看 API 请求
- **Console** 标签查看 JavaScript 错误
- **Vue DevTools** 扩展调试 Vue 组件

#### 2. 热重载调试
- 修改 `.vue` 文件会自动重载
- 修改 `.scss` 文件会自动重载
- 修改 `vue.config.js` 需要重启开发服务器

#### 3. 代理调试
```bash
# 查看代理是否工作
curl http://localhost:8080/api/ping
# 应该返回与 http://localhost:3000/api/ping 相同的结果
```

#### 4. 构建调试
```bash
# 检查构建错误
npm run build

# 分析构建包大小
npm run build -- --analyze
```

## 常见问题解决

### 1. 500 错误
**原因:** 后端 API 服务器未启动或配置错误
**解决:**
1. 确保后端服务器正在运行
2. 检查 `config.json` 配置
3. 查看后端日志输出

### 2. CORS 错误
**原因:** 跨域请求被阻止
**解决:**
- 开发模式下使用代理 (已配置)
- 生产模式下确保前后端同域

### 3. 登录失败
**原因:** 用户不存在或密码错误
**解决:**
```bash
# 创建管理员用户
./bin/semaphore users add --admin --login admin --name Admin --email admin@localhost --password admin
```

### 4. 前端编译错误
**原因:** 依赖版本冲突或语法错误
**解决:**
```bash
# 清除依赖重新安装
rm -rf node_modules package-lock.json
npm install

# 检查 ESLint 错误
npm run lint
```

## 生产部署

### 1. 构建前端
```bash
cd web
npm run build
```

### 2. 启动后端
```bash
# 生产模式启动 (前端文件已构建到 api/public/)
./bin/semaphore server --config config.json
```

### 3. 访问应用
- 访问: http://localhost:3000
- 默认用户: admin / admin (如果已创建)

## 开发工作流

### 1. 日常开发
```bash
# 终端 1: 启动后端
./bin/semaphore server --config config.json

# 终端 2: 启动前端
cd web && npm run serve

# 浏览器访问: http://localhost:8080
```

### 2. 代码修改
- 后端修改需要重新编译和重启
- 前端修改会自动热重载

### 3. 测试
```bash
# 前端测试
cd web && npm run test

# 前端 lint 检查
cd web && npm run lint
```

## 高级调试技巧

### 1. 网络请求调试

#### 查看所有 API 请求
```bash
# 在浏览器开发者工具中
# Network -> XHR -> 查看所有 AJAX 请求
# 重点关注:
# - 请求 URL
# - 响应状态码
# - 请求/响应头
# - 请求/响应体
```

#### 使用 curl 测试 API
```bash
# 获取系统信息
curl -v http://localhost:3000/api/info

# 登录获取 Cookie
curl -v -c cookies.txt \
  -H "Content-Type: application/json" \
  -X POST http://localhost:3000/api/auth/login \
  -d '{"auth":"admin","password":"admin"}'

# 使用 Cookie 访问受保护的 API
curl -v -b cookies.txt http://localhost:3000/api/user
```

### 2. 数据库调试

#### BoltDB 数据查看
```bash
# 安装 bolt 命令行工具 (可选)
go install go.etcd.io/bbolt/cmd/bolt@latest

# 查看数据库结构
bolt info database.boltdb

# 查看所有 bucket
bolt buckets database.boltdb
```

#### 数据库备份和恢复
```bash
# 备份数据库
cp database.boltdb backup_$(date +%Y%m%d_%H%M%S).boltdb

# 恢复数据库
cp backup_20240101_120000.boltdb database.boltdb
```

### 3. 性能调试

#### 前端性能分析
```bash
# 构建分析
cd web
npm run build -- --analyze

# 查看包大小
npm run build
ls -lh ../api/public/js/
```

#### 后端性能监控
```bash
# 启用性能分析 (如果支持)
./bin/semaphore server --config config.json --log-level DEBUG

# 监控进程资源使用
top -p $(pgrep semaphore)
```

## 故障排除清单

### 启动失败检查清单

1. **检查端口占用**
   ```bash
   lsof -i :3000  # 检查后端端口
   lsof -i :8080  # 检查前端端口
   ```

2. **检查文件权限**
   ```bash
   ls -la bin/semaphore      # 确保可执行
   ls -la database.boltdb    # 确保可读写
   ls -la config.json        # 确保可读
   ```

3. **检查磁盘空间**
   ```bash
   df -h /tmp                # 检查临时目录空间
   df -h .                   # 检查当前目录空间
   ```

4. **检查依赖**
   ```bash
   cd web
   npm ls                    # 检查前端依赖
   ```

### 运行时错误检查清单

1. **API 连接问题**
   - 确认后端服务器运行状态
   - 检查防火墙设置
   - 验证代理配置

2. **认证问题**
   - 检查用户是否存在
   - 验证密码正确性
   - 查看 Cookie 设置

3. **权限问题**
   - 检查用户角色
   - 验证项目权限
   - 查看访问控制

## 开发最佳实践

### 1. 代码修改流程
```bash
# 1. 创建功能分支
git checkout -b feature/new-feature

# 2. 修改代码
# 前端: 修改 web/src/ 下的文件
# 后端: 修改 Go 源码 (如果有)

# 3. 测试修改
npm run lint          # 前端代码检查
npm run test          # 前端测试

# 4. 提交代码
git add .
git commit -m "feat: add new feature"
```

### 2. 调试技巧
- 使用浏览器开发者工具的断点调试
- 在 Vue 组件中使用 `console.log()` 输出调试信息
- 使用 Vue DevTools 查看组件状态
- 监控网络请求的状态码和响应时间

### 3. 性能优化
- 使用 Vue 的懒加载路由
- 优化图片和静态资源
- 启用 gzip 压缩
- 使用 CDN 加速静态资源

## 常用命令速查

### 后端命令
```bash
# 启动服务器
./bin/semaphore server --config config.json

# 查看版本
./bin/semaphore version

# 用户管理
./bin/semaphore users add --admin --login admin --name Admin --email admin@localhost
./bin/semaphore users list

# 数据库迁移
./bin/semaphore migrate --config config.json
```

### 前端命令
```bash
cd web

# 开发
npm run serve          # 启动开发服务器
npm run build          # 构建生产版本
npm run lint           # 代码检查
npm run lint -- --fix  # 自动修复代码格式

# 依赖管理
npm install            # 安装依赖
npm update             # 更新依赖
npm audit              # 安全检查
```

### 系统命令
```bash
# 进程管理
ps aux | grep semaphore    # 查看进程
kill -9 $(pgrep semaphore) # 强制停止

# 日志查看
tail -f /var/log/semaphore.log  # 查看日志 (如果有)

# 网络调试
netstat -tlnp | grep :3000     # 检查端口监听
curl -I http://localhost:3000/api/ping  # 健康检查
```

这个指南涵盖了 Semaphore 项目的完整开发环境设置、调试流程和故障排除方法。
