name: Release

'on':
  push:
    tags:
      - 'v[0-9]+.[0-9]+.[0-9]+'
      - v*-rc*

jobs:
  release:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout source
        uses: actions/checkout@v4

      - name: Setup golang
        uses: actions/setup-go@v5
        with:
          go-version: '^1.21.0'

      - name: Setup nodejs
        uses: actions/setup-node@v4
        with:
          node-version: '16'
          cache: 'npm'
          cache-dependency-path: web/package-lock.json

      - name: Install go-task
        run: |
          go install github.com/go-task/task/v3/cmd/task@latest

      - name: Install rpm
        run: |
          sudo apt update && sudo apt-get install rpm

      - name: Install deps
        run: |
          task deps

      - name: Import gnupg
        run: |
          echo "${{ secrets.GPG_KEY }}" | tr " " "\n" | base64 -d | gpg --import --batch
          gpg --sign -u "58A7 CC3D 8A9C A2E5 BB5C 141D 4064 23EA F814 63CA" --pinentry-mode loopback --yes --batch --passphrase "${{ secrets.GPG_PASS }}" --output unlock.sig --detach-sign README.md
          rm -f unlock.sig

      - name: Reset repo
        run: |
          git reset --hard

      - name: Run release
        run: |
          GITHUB_TOKEN=${{ secrets.GH_TOKEN }} task release:prod

  deploy-prod:
    runs-on: ubuntu-latest
    if: github.repository_owner == 'semaphoreui'

    steps:
      - name: Checkout source
        uses: actions/checkout@v4

      - name: Setup qemu
        id: qemu
        uses: docker/setup-qemu-action@v3

      - name: Setup buildx
        id: buildx
        uses: docker/setup-buildx-action@v3

      - name: Hub login
        uses: docker/login-action@v3
        if: github.event_name != 'pull_request'
        with:
          username: ${{ secrets.DOCKER_USER }}
          password: ${{ secrets.DOCKER_PASS }}



      - name: Server meta
        id: server
        uses: docker/metadata-action@v5
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          images: |
            semaphoreui/semaphore
          labels: |
            org.opencontainers.image.vendor=SemaphoreUI
            maintainer=Semaphore UI <<EMAIL>>
          tags: |
            type=raw,value=${{ github.ref_name }}
          flavor: |
            latest=true

      - name: Server build
        uses: docker/build-push-action@v5
        with:
          builder: ${{ steps.buildx.outputs.name }}
          context: .
          file: deployment/docker/server/Dockerfile
          platforms: linux/amd64,linux/arm64 # ,linux/arm/v6
          push: ${{ github.event_name != 'pull_request' }}
          labels: ${{ steps.server.outputs.labels }}
          tags: ${{ steps.server.outputs.tags }}

      - name: Server build with Ansible 2.16.5
        uses: docker/build-push-action@v5
        with:
          builder: ${{ steps.buildx.outputs.name }}
          context: .
          build-args: |
            ANSIBLE_VERSION=9.4.0
          file: deployment/docker/server/Dockerfile
          platforms: linux/amd64,linux/arm64 # ,linux/arm/v6
          push: ${{ github.event_name != 'pull_request' }}
          labels: ${{ steps.server.outputs.labels }}
          tags: semaphoreui/semaphore:${{ github.ref_name }}-ansible2.16.5

      - name: Server build with PowerShell 7.5.0
        uses: docker/build-push-action@v5
        with:
          builder: ${{ steps.buildx.outputs.name }}
          context: .
          build-args: |
            POWERSHELL_VERSION=7.5.0
            SEMAPHORE_IMAGE=semaphoreui/semaphore
            SEMAPHORE_VERSION=${{ github.ref_name }}
          file: deployment/docker/server/powershell/Dockerfile
          platforms: linux/amd64
          push: ${{ github.event_name != 'pull_request' }}
          labels: ${{ steps.server.outputs.labels }}
          tags: semaphoreui/semaphore:${{ github.ref_name }}-powershell7.5.0



      - name: Runner meta
        id: runner
        uses: docker/metadata-action@v5
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          images: |
            semaphoreui/runner
          labels: |
            org.opencontainers.image.vendor=SemaphoreUI
            maintainer=Semaphore UI <<EMAIL>>
          tags: |
            type=raw,value=${{ github.ref_name }}
          flavor: |
            latest=true

      - name: Runner build
        uses: docker/build-push-action@v5
        with:
          builder: ${{ steps.buildx.outputs.name }}
          context: .
          file: deployment/docker/runner/Dockerfile
          platforms: linux/amd64,linux/arm64 #,linux/arm/v6
          push: ${{ github.event_name != 'pull_request' }}
          labels: ${{ steps.runner.outputs.labels }}
          tags: ${{ steps.runner.outputs.tags }}

      - name: Runner build with Ansible 2.16.5
        uses: docker/build-push-action@v5
        with:
          builder: ${{ steps.buildx.outputs.name }}
          context: .
          file: deployment/docker/runner/Dockerfile
          platforms: linux/amd64,linux/arm64 #,linux/arm/v6
          push: ${{ github.event_name != 'pull_request' }}
          labels: ${{ steps.runner.outputs.labels }}
          tags: semaphoreui/runner:${{ github.ref_name }}-ansible2.16.5

      - name: Runner build with PowerShell 7.5.0
        uses: docker/build-push-action@v5
        with:
          builder: ${{ steps.buildx.outputs.name }}
          context: .
          build-args: |
            POWERSHELL_VERSION=7.5.0
            SEMAPHORE_IMAGE=semaphoreui/runner
            SEMAPHORE_VERSION=${{ github.ref_name }}
          file: deployment/docker/server/powershell/Dockerfile
          platforms: linux/amd64
          push: ${{ github.event_name != 'pull_request' }}
          labels: ${{ steps.runner.outputs.labels }}
          tags: semaphoreui/runner:${{ github.ref_name }}-powershell7.5.0
