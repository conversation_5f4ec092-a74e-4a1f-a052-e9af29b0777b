---

name: Problem
description: You have encountered problems when using Semaphore
title: "Problem: "
labels: ['problem', 'triage']

body:
  - type: markdown
    attributes:
      value: |
        Please make sure to go through these steps **before opening an issue**:
        
        - [ ] Read the [documentation](https://docs.semaphoreui.com/)
          - [ ] Read the [troubleshooting guide](https://docs.semaphoreui.com/administration-guide/troubleshooting)
          - [ ] Read the [documentation regarding manual installations](https://docs.semaphoreui.com/administration-guide/installation_manually) if you don't use docker

        - [ ] Check if there are existing [issues](https://github.com/semaphoreui/semaphore/issues) or [discussions](https://github.com/semaphoreui/semaphore/discussions) regarding your topic

  - type: textarea
    id: problem
    attributes:
      label: Issue
      description: |
        Describe the problem you encountered and tell us what you would have expected to happen

    validations:
      required: true

  - type: dropdown
    id: impact
    attributes:
      label: Impact
      description: |
        What parts of Semaphore are impacted by the problem?

      multiple: true
      options:
        - Web-Frontend (what users interact with)
        - Web-Backend (APIs)
        - Service (scheduled tasks, alerts)
        - Ansible (task execution)
        - Configuration
        - Database
        - Docker
        - Semaphore Project
        - Other
    validations:
      required: true

  - type: dropdown
    id: install-method
    attributes:
      label: Installation method
      description: |
        How did you install Semaphore?

      multiple: false
      options:
        - Docker
        - Package
        - Binary
        - Snap
    validations:
      required: true


  - type: dropdown
    id: databases
    attributes:
      label: Database
      description: |
        What database you use?

      multiple: true
      options:
        - MySQL
        - BoltDB
        - Postgres


  - type: dropdown
    id: browsers
    attributes:
      label: Browser
      description: |
        If the problem occurs in the Semaphore WebUI - in what browsers do you see it?

      multiple: true
      options:
        - Firefox
        - Chrome
        - Safari
        - Microsoft Edge
        - Opera

  - type: textarea
    id: version-semaphore
    attributes:
      label: Semaphore Version
      description: |
        What version of Semaphore are you running?
        > Command: `semaphore version`
    validations:
      required: true

  - type: textarea
    id: version-ansible
    attributes:
      label: Ansible Version
      description: |
        If your problem occurs when executing a task:
        > What version of Ansible are you running?
        > Command: `ansible --version`

        If your problem occurs when executing a specific Ansible Module:
        > Provide the Ansible Module versions!
        > Command: `ansible-galaxy collection list`

      render: bash
    validations:
      required: false

  - type: textarea
    id: logs
    attributes:
      label: Logs & errors
      description: |
        Provide logs and error messages you have encountered!
        
        Logs of the service:
        > Docker command: `docker logs <container-name>`
        > Systemd command: `journalctl -u <serivce-name> --no-pager --full -n 250`

        If the error occurs in the WebUI:
        > please add a screenshot
        > check your browser console for errors (`F12` in most browsers)

        Tip: You can attach images or log files by clicking this area to highlight it and then dragging files in.

    validations:
      required: false

  - type: textarea
    id: manual-installation
    attributes:
      label: Manual installation - system information
      description: |
        If you have installed Semaphore using the package or binary:
        
        Please share your operating system & -version!
        > Command: `uname -a`
        
        What reverse proxy are you using?
    validations:
      required: false

  - type: textarea
    id: config
    attributes:
      label: Configuration
      description: |
        Please provide Semaphore configuration related to your problem - like:
        * Config file options
        * Environment variables
        * WebUI configuration
          * Task templates
          * Inventories
          * Environment
          * Repositories
          * ...

    validations:
      required: false

  - type: textarea
    id: additional
    attributes:
      label: Additional information
      description: |
        Do you have additional information that could help troubleshoot the problem?

    validations:
      required: false
