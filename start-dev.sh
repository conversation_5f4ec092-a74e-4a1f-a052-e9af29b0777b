#!/bin/bash

# Semaphore 开发环境启动脚本
# 使用方法: ./start-dev.sh [选项]
# 选项:
#   --backend-only    只启动后端
#   --frontend-only   只启动前端
#   --help           显示帮助信息

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Semaphore 开发环境启动脚本"
    echo ""
    echo "使用方法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --backend-only     只启动后端 API 服务器"
    echo "  --frontend-only    只启动前端开发服务器"
    echo "  --help            显示此帮助信息"
    echo ""
    echo "默认行为: 同时启动前后端服务器"
    echo ""
    echo "端口信息:"
    echo "  后端 API: http://localhost:3000"
    echo "  前端开发: http://localhost:8080"
    echo ""
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查后端二进制文件
    if [ ! -f "bin/semaphore" ]; then
        log_error "后端二进制文件 bin/semaphore 不存在"
        exit 1
    fi
    
    if [ ! -x "bin/semaphore" ]; then
        log_error "后端二进制文件 bin/semaphore 不可执行"
        exit 1
    fi
    
    # 检查配置文件
    if [ ! -f "config.json" ]; then
        log_error "配置文件 config.json 不存在"
        exit 1
    fi
    
    # 检查数据库文件
    if [ ! -f "database.boltdb" ]; then
        log_warning "数据库文件 database.boltdb 不存在，可能需要运行初始化"
        log_info "运行: ./bin/semaphore setup"
    fi
    
    # 检查 Node.js (如果需要启动前端)
    if [ "$FRONTEND_ONLY" = true ] || [ "$BACKEND_ONLY" != true ]; then
        if ! command -v node &> /dev/null; then
            log_error "Node.js 未安装，无法启动前端开发服务器"
            exit 1
        fi
        
        if [ ! -d "web/node_modules" ]; then
            log_warning "前端依赖未安装，正在安装..."
            cd web && npm install && cd ..
        fi
    fi
    
    log_success "依赖检查完成"
}

# 检查端口占用
check_ports() {
    log_info "检查端口占用..."
    
    if [ "$BACKEND_ONLY" = true ] || [ "$FRONTEND_ONLY" != true ]; then
        if lsof -i :3000 &> /dev/null; then
            log_warning "端口 3000 已被占用"
            log_info "运行以下命令释放端口: lsof -ti:3000 | xargs kill -9"
        fi
    fi
    
    if [ "$FRONTEND_ONLY" = true ] || [ "$BACKEND_ONLY" != true ]; then
        if lsof -i :8080 &> /dev/null; then
            log_warning "端口 8080 已被占用"
            log_info "运行以下命令释放端口: lsof -ti:8080 | xargs kill -9"
        fi
    fi
}

# 启动后端
start_backend() {
    log_info "启动后端 API 服务器..."
    
    # 在后台启动后端
    nohup ./bin/semaphore server --config config.json > backend.log 2>&1 &
    BACKEND_PID=$!
    
    # 等待后端启动
    log_info "等待后端服务器启动..."
    for i in {1..30}; do
        if curl -s http://localhost:3000/api/ping > /dev/null 2>&1; then
            log_success "后端服务器启动成功 (PID: $BACKEND_PID)"
            log_info "后端 API: http://localhost:3000"
            log_info "后端日志: tail -f backend.log"
            return 0
        fi
        sleep 1
    done
    
    log_error "后端服务器启动失败"
    log_info "查看日志: cat backend.log"
    exit 1
}

# 启动前端
start_frontend() {
    log_info "启动前端开发服务器..."
    
    cd web
    
    # 在后台启动前端
    nohup npm run serve > ../frontend.log 2>&1 &
    FRONTEND_PID=$!
    
    cd ..
    
    # 等待前端启动
    log_info "等待前端服务器启动..."
    for i in {1..60}; do
        if curl -s http://localhost:8080 > /dev/null 2>&1; then
            log_success "前端服务器启动成功 (PID: $FRONTEND_PID)"
            log_info "前端开发: http://localhost:8080"
            log_info "前端日志: tail -f frontend.log"
            return 0
        fi
        sleep 1
    done
    
    log_error "前端服务器启动失败"
    log_info "查看日志: cat frontend.log"
    exit 1
}

# 清理函数
cleanup() {
    log_info "正在停止服务器..."
    
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
        log_info "后端服务器已停止"
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        log_info "前端服务器已停止"
    fi
    
    # 清理可能残留的进程
    pkill -f "semaphore server" 2>/dev/null || true
    pkill -f "npm run serve" 2>/dev/null || true
    
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 解析命令行参数
BACKEND_ONLY=false
FRONTEND_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --backend-only)
            BACKEND_ONLY=true
            shift
            ;;
        --frontend-only)
            FRONTEND_ONLY=true
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主逻辑
main() {
    log_info "Semaphore 开发环境启动脚本"
    log_info "==============================="
    
    check_dependencies
    check_ports
    
    if [ "$FRONTEND_ONLY" = true ]; then
        log_info "只启动前端开发服务器"
        start_frontend
    elif [ "$BACKEND_ONLY" = true ]; then
        log_info "只启动后端 API 服务器"
        start_backend
    else
        log_info "启动前后端服务器"
        start_backend
        start_frontend
    fi
    
    log_success "所有服务器启动完成！"
    log_info "==============================="
    
    if [ "$BACKEND_ONLY" != true ]; then
        log_info "🌐 前端开发: http://localhost:8080"
    fi
    
    if [ "$FRONTEND_ONLY" != true ]; then
        log_info "🔧 后端 API: http://localhost:3000"
    fi
    
    log_info "📋 按 Ctrl+C 停止所有服务器"
    log_info "==============================="
    
    # 保持脚本运行
    wait
}

# 运行主函数
main
